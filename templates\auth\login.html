<!DOCTYPE html>
<html lang="{{ CURRENT_LANGUAGE }}" dir="{{ 'rtl' if CURRENT_LANGUAGE == 'ar' else 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% if CURRENT_LANGUAGE == 'ar' %}تسجيل الدخول - {{ APP_NAME }}{% else %}Login - {{ APP_NAME_EN }}{% endif %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic Support -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/login.css') }}" rel="stylesheet">
    
    {% if CURRENT_LANGUAGE == 'ar' %}
    <link href="{{ url_for('static', filename='css/rtl.css') }}" rel="stylesheet">
    {% endif %}
</head>
<body class="login-page">
    <div class="login-container">
        <div class="container-fluid h-100">
            <div class="row h-100">
                <!-- Left side - Login form -->
                <div class="col-lg-6 col-md-8 mx-auto d-flex align-items-center">
                    <div class="login-form-container w-100">
                        <div class="text-center mb-4">
                            <div class="login-logo">
                                <i class="fas fa-pills"></i>
                            </div>
                            <h2 class="login-title">
                                {% if CURRENT_LANGUAGE == 'ar' %}
                                    {{ APP_NAME }}
                                {% else %}
                                    {{ APP_NAME_EN }}
                                {% endif %}
                            </h2>
                            <p class="login-subtitle">
                                {% if CURRENT_LANGUAGE == 'ar' %}
                                    نظام إدارة الصيدليات الاحترافي
                                {% else %}
                                    Professional Pharmacy Management System
                                {% endif %}
                            </p>
                        </div>
                        
                        <!-- Flash messages -->
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}
                        
                        <!-- Login form -->
                        <form method="POST" class="login-form">
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user me-2"></i>
                                    {% if CURRENT_LANGUAGE == 'ar' %}اسم المستخدم{% else %}Username{% endif %}
                                </label>
                                <input type="text" class="form-control form-control-lg" id="username" name="username" 
                                       placeholder="{% if CURRENT_LANGUAGE == 'ar' %}أدخل اسم المستخدم{% else %}Enter username{% endif %}" 
                                       required autofocus>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-2"></i>
                                    {% if CURRENT_LANGUAGE == 'ar' %}كلمة المرور{% else %}Password{% endif %}
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control form-control-lg" id="password" name="password" 
                                           placeholder="{% if CURRENT_LANGUAGE == 'ar' %}أدخل كلمة المرور{% else %}Enter password{% endif %}" 
                                           required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                        <i class="fas fa-eye" id="password-toggle-icon"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="remember" name="remember">
                                <label class="form-check-label" for="remember">
                                    {% if CURRENT_LANGUAGE == 'ar' %}تذكرني{% else %}Remember me{% endif %}
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-primary btn-lg w-100 mb-3">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                {% if CURRENT_LANGUAGE == 'ar' %}تسجيل الدخول{% else %}Login{% endif %}
                            </button>
                        </form>
                        
                        <!-- Language switcher -->
                        <div class="text-center mt-4">
                            <div class="language-switcher">
                                {% for code, name in LANGUAGES.items() %}
                                    <a href="{{ url_for('set_language', language=code) }}" 
                                       class="btn btn-outline-secondary btn-sm {% if code == CURRENT_LANGUAGE %}active{% endif %}">
                                        {{ name }}
                                    </a>
                                {% endfor %}
                            </div>
                        </div>
                        
                        <!-- Default credentials info -->
                        <div class="mt-4 p-3 bg-light rounded">
                            <h6 class="mb-2">
                                {% if CURRENT_LANGUAGE == 'ar' %}بيانات الدخول الافتراضية:{% else %}Default Login Credentials:{% endif %}
                            </h6>
                            <p class="mb-1"><strong>{% if CURRENT_LANGUAGE == 'ar' %}اسم المستخدم:{% else %}Username:{% endif %}</strong> admin</p>
                            <p class="mb-0"><strong>{% if CURRENT_LANGUAGE == 'ar' %}كلمة المرور:{% else %}Password:{% endif %}</strong> admin123</p>
                        </div>
                    </div>
                </div>
                
                <!-- Right side - Background image/pattern -->
                <div class="col-lg-6 d-none d-lg-block">
                    <div class="login-bg">
                        <div class="login-bg-overlay">
                            <div class="login-bg-content">
                                <h3>
                                    {% if CURRENT_LANGUAGE == 'ar' %}
                                        مرحباً بك في نظام إدارة الصيدليات
                                    {% else %}
                                        Welcome to Pharmacy Management System
                                    {% endif %}
                                </h3>
                                <p>
                                    {% if CURRENT_LANGUAGE == 'ar' %}
                                        نظام شامل ومتطور لإدارة جميع عمليات الصيدلية بكفاءة وسهولة
                                    {% else %}
                                        A comprehensive and advanced system for managing all pharmacy operations efficiently and easily
                                    {% endif %}
                                </p>
                                <div class="features-list">
                                    <div class="feature-item">
                                        <i class="fas fa-check-circle"></i>
                                        <span>
                                            {% if CURRENT_LANGUAGE == 'ar' %}نقطة بيع احترافية{% else %}Professional POS{% endif %}
                                        </span>
                                    </div>
                                    <div class="feature-item">
                                        <i class="fas fa-check-circle"></i>
                                        <span>
                                            {% if CURRENT_LANGUAGE == 'ar' %}إدارة المخزون الذكية{% else %}Smart Inventory Management{% endif %}
                                        </span>
                                    </div>
                                    <div class="feature-item">
                                        <i class="fas fa-check-circle"></i>
                                        <span>
                                            {% if CURRENT_LANGUAGE == 'ar' %}تقارير شاملة{% else %}Comprehensive Reports{% endif %}
                                        </span>
                                    </div>
                                    <div class="feature-item">
                                        <i class="fas fa-check-circle"></i>
                                        <span>
                                            {% if CURRENT_LANGUAGE == 'ar' %}دعم متعدد اللغات{% else %}Multi-language Support{% endif %}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('password-toggle-icon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
        
        // Auto-focus on username field
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username').focus();
        });
    </script>
</body>
</html>
