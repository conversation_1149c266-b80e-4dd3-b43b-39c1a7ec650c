#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الصيدليات الاحترافي
Professional Pharmacy Management System

تطبيق ويب شامل لإدارة الصيدليات باستخدام Flask وMySQL
مع دعم اللغة العربية والإنجليزية والجنيه المصري
"""

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_login import LoginManager, login_user, logout_user, login_required, current_user
from flask_wtf.csrf import CSRFProtect
from flask_babel import Babel, gettext, ngettext, lazy_gettext
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date
import os
from dotenv import load_dotenv
import pymysql

# تحميل متغيرات البيئة
load_dotenv()

# إنشاء التطبيق
app = Flask(__name__)

# الإعدادات الأساسية
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY') or 'pharmacy-management-secret-key-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL') or \
    'mysql+pymysql://root:@localhost/pharmacy_management'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إعدادات اللغة
app.config['LANGUAGES'] = {
    'ar': 'العربية',
    'en': 'English'
}
app.config['BABEL_DEFAULT_LOCALE'] = 'ar'
app.config['BABEL_DEFAULT_TIMEZONE'] = 'Africa/Cairo'

# تهيئة الإضافات
db = SQLAlchemy(app)
migrate = Migrate(app, db)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'auth.login'
login_manager.login_message = lazy_gettext('يرجى تسجيل الدخول للوصول لهذه الصفحة')
csrf = CSRFProtect(app)
babel = Babel(app)

@babel.localeselector
def get_locale():
    """تحديد اللغة الحالية"""
    # التحقق من اللغة المحفوظة في الجلسة
    if 'language' in session:
        return session['language']
    # التحقق من اللغة المفضلة في المتصفح
    return request.accept_languages.best_match(app.config['LANGUAGES'].keys()) or 'ar'

# استيراد النماذج
from models import User, Category, Product, Customer, Supplier, Sale, SaleItem, Purchase, PurchaseItem

@login_manager.user_loader
def load_user(user_id):
    """تحميل المستخدم"""
    return User.query.get(int(user_id))

# استيراد المسارات
from routes import auth, dashboard, pos, inventory, customers, suppliers, sales, purchases, reports, settings

# تسجيل المسارات
app.register_blueprint(auth.bp)
app.register_blueprint(dashboard.bp)
app.register_blueprint(pos.bp)
app.register_blueprint(inventory.bp)
app.register_blueprint(customers.bp)
app.register_blueprint(suppliers.bp)
app.register_blueprint(sales.bp)
app.register_blueprint(purchases.bp)
app.register_blueprint(reports.bp)
app.register_blueprint(settings.bp)

@app.route('/')
def index():
    """الصفحة الرئيسية"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))
    return redirect(url_for('auth.login'))

@app.route('/set_language/<language>')
def set_language(language=None):
    """تغيير اللغة"""
    if language in app.config['LANGUAGES']:
        session['language'] = language
    return redirect(request.referrer or url_for('index'))

@app.context_processor
def inject_conf_vars():
    """حقن المتغيرات في القوالب"""
    return {
        'LANGUAGES': app.config['LANGUAGES'],
        'CURRENT_LANGUAGE': session.get('language', 'ar'),
        'APP_NAME': 'نظام إدارة الصيدليات',
        'APP_NAME_EN': 'Pharmacy Management System',
        'CURRENCY': 'ج.م',
        'CURRENCY_EN': 'EGP'
    }

@app.template_filter('currency')
def currency_filter(amount):
    """فلتر تنسيق العملة"""
    if amount is None:
        amount = 0
    return f"{amount:,.2f} ج.م"

@app.template_filter('date_format')
def date_format_filter(date_obj, format='%Y-%m-%d'):
    """فلتر تنسيق التاريخ"""
    if date_obj:
        return date_obj.strftime(format)
    return ''

@app.errorhandler(404)
def not_found_error(error):
    """صفحة الخطأ 404"""
    return render_template('errors/404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    """صفحة الخطأ 500"""
    db.session.rollback()
    return render_template('errors/500.html'), 500

def create_tables():
    """إنشاء الجداول"""
    with app.app_context():
        db.create_all()
        
        # إنشاء مستخدم افتراضي إذا لم يكن موجوداً
        if not User.query.filter_by(username='admin').first():
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                full_name_ar='المدير العام',
                full_name_en='System Administrator',
                role='admin',
                is_active=True
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
            
        # إنشاء فئات افتراضية
        default_categories = [
            {'name_ar': 'أدوية', 'name_en': 'Medicines', 'description_ar': 'الأدوية والعقاقير الطبية'},
            {'name_ar': 'مستحضرات تجميل', 'name_en': 'Cosmetics', 'description_ar': 'مستحضرات العناية والتجميل'},
            {'name_ar': 'مكملات غذائية', 'name_en': 'Supplements', 'description_ar': 'الفيتامينات والمكملات الغذائية'},
            {'name_ar': 'أدوات طبية', 'name_en': 'Medical Devices', 'description_ar': 'الأدوات والمعدات الطبية'},
        ]
        
        for cat_data in default_categories:
            if not Category.query.filter_by(name_ar=cat_data['name_ar']).first():
                category = Category(**cat_data)
                db.session.add(category)
        
        try:
            db.session.commit()
            print("تم إنشاء الجداول والبيانات الافتراضية بنجاح")
        except Exception as e:
            db.session.rollback()
            print(f"خطأ في إنشاء البيانات الافتراضية: {e}")

if __name__ == '__main__':
    # إنشاء الجداول
    create_tables()
    
    # تشغيل التطبيق
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True
    )
