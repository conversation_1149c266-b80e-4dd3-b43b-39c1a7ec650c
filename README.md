# نظام إدارة الصيدليات الاحترافي
# Professional Pharmacy Management System

نظام ويب شامل لإدارة الصيدليات باستخدام Python (Flask) وMySQL مع دعم اللغة العربية والإنجليزية والجنيه المصري.

## المميزات الرئيسية

- **واجهة مستخدم احترافية**: تصميم عصري وسهل الاستخدام مع دعم الوضع الليلي والنهاري
- **دعم متعدد اللغات**: دعم كامل للغة العربية والإنجليزية
- **نقطة بيع متقدمة**: نظام نقطة بيع احترافي مع دعم الباركود وتعليق الفواتير
- **وحدات بيع متعددة**: دعم بيع المنتجات بوحدات مختلفة (قطعة، شريط، علبة، إلخ)
- **إدارة المخزون**: تتبع المخزون والتنبيهات عند انخفاض المخزون
- **إدارة العملاء والموردين**: قاعدة بيانات شاملة للعملاء والموردين
- **التقارير**: تقارير مفصلة للمبيعات والمشتريات والمخزون
- **إدارة المستخدمين**: نظام صلاحيات متعدد المستويات
- **تصميم متجاوب**: يعمل على جميع أحجام الشاشات والأجهزة

## المتطلبات التقنية

- Python 3.8+
- MySQL 5.7+
- Flask 2.0+
- Web Browser (Chrome, Firefox, Edge, Safari)

## التثبيت

### 1. إعداد قاعدة البيانات

```sql
CREATE DATABASE pharmacy_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'pharmacy_user'@'localhost' IDENTIFIED BY 'password';
GRANT ALL PRIVILEGES ON pharmacy_management.* TO 'pharmacy_user'@'localhost';
FLUSH PRIVILEGES;
```

### 2. تثبيت المتطلبات

```bash
# إنشاء بيئة افتراضية
python -m venv venv

# تفعيل البيئة الافتراضية
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# تثبيت المكتبات المطلوبة
pip install -r requirements.txt
```

### 3. إعداد ملف البيئة

قم بإنشاء ملف `.env` في المجلد الرئيسي للمشروع وأضف الإعدادات التالية:

```
SECRET_KEY=your-secret-key
DATABASE_URL=mysql+pymysql://pharmacy_user:password@localhost/pharmacy_management
DEBUG=True
DEFAULT_LANGUAGE=ar
TIMEZONE=Africa/Cairo
```

### 4. تهيئة قاعدة البيانات

```bash
flask db init
flask db migrate -m "Initial migration"
flask db upgrade
```

### 5. تشغيل التطبيق

```bash
flask run
```

## الاستخدام

1. افتح المتصفح وانتقل إلى `http://localhost:5000`
2. سجل الدخول باستخدام بيانات الاعتماد الافتراضية:
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin123`
3. قم بتغيير كلمة المرور الافتراضية من صفحة الملف الشخصي

## الوظائف الرئيسية

### نقطة البيع (POS)
- إنشاء فواتير جديدة
- إضافة منتجات للفاتورة
- تطبيق خصومات على المنتجات أو الفاتورة بالكامل
- تعليق الفواتير واستكمالها لاحقاً
- طباعة الفواتير

### إدارة المخزون
- إضافة وتعديل المنتجات
- تتبع المخزون والتنبيه عند انخفاضه
- تتبع تواريخ انتهاء الصلاحية
- إدارة الفئات

### إدارة العملاء والموردين
- إضافة وتعديل بيانات العملاء والموردين
- تتبع مشتريات العملاء
- تتبع المدفوعات والديون

### التقارير
- تقارير المبيعات (يومية، أسبوعية، شهرية)
- تقارير المشتريات
- تقارير المخزون
- تقارير العملاء والموردين
- تقارير مالية

## المساهمة في المشروع

نرحب بمساهماتكم في تطوير هذا المشروع. يرجى اتباع الخطوات التالية:

1. قم بعمل Fork للمشروع
2. قم بإنشاء فرع جديد للميزة التي تريد إضافتها (`git checkout -b feature/amazing-feature`)
3. قم بعمل Commit للتغييرات (`git commit -m 'Add some amazing feature'`)
4. قم بدفع الفرع إلى GitHub (`git push origin feature/amazing-feature`)
5. قم بفتح طلب Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## الاتصال والدعم

للاستفسارات والدعم، يرجى التواصل عبر:
- البريد الإلكتروني: <EMAIL>
- الموقع الإلكتروني: https://pharmacy-system.com
