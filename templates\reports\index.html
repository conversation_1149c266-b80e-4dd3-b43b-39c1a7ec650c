{% extends "base.html" %}

{% block title %}
    {% if CURRENT_LANGUAGE == 'ar' %}التقارير - {{ APP_NAME }}{% else %}Reports - {{ APP_NAME_EN }}{% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            {% if CURRENT_LANGUAGE == 'ar' %}التقارير{% else %}Reports{% endif %}
        </h1>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-primary" id="refreshBtn">
                <i class="fas fa-sync-alt"></i>
                {% if CURRENT_LANGUAGE == 'ar' %}تحديث{% else %}Refresh{% endif %}
            </button>
            <button class="btn btn-primary" id="printReportBtn">
                <i class="fas fa-print"></i>
                {% if CURRENT_LANGUAGE == 'ar' %}طباعة{% else %}Print{% endif %}
            </button>
        </div>
    </div>

    <!-- أنواع التقارير -->
    <div class="row mb-4">
        <div class="col-md-3 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-chart-line me-2"></i>
                        {% if CURRENT_LANGUAGE == 'ar' %}تقارير المبيعات{% else %}Sales Reports{% endif %}
                    </h5>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item">
                            <a href="#" class="report-link" data-report="daily-sales">
                                {% if CURRENT_LANGUAGE == 'ar' %}المبيعات اليومية{% else %}Daily Sales{% endif %}
                            </a>
                        </li>
                        <li class="list-group-item">
                            <a href="#" class="report-link" data-report="monthly-sales">
                                {% if CURRENT_LANGUAGE == 'ar' %}المبيعات الشهرية{% else %}Monthly Sales{% endif %}
                            </a>
                        </li>
                        <li class="list-group-item">
                            <a href="#" class="report-link" data-report="sales-by-product">
                                {% if CURRENT_LANGUAGE == 'ar' %}المبيعات حسب المنتج{% else %}Sales by Product{% endif %}
                            </a>
                        </li>
                        <li class="list-group-item">
                            <a href="#" class="report-link" data-report="sales-by-category">
                                {% if CURRENT_LANGUAGE == 'ar' %}المبيعات حسب الفئة{% else %}Sales by Category{% endif %}
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-boxes me-2"></i>
                        {% if CURRENT_LANGUAGE == 'ar' %}تقارير المخزون{% else %}Inventory Reports{% endif %}
                    </h5>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item">
                            <a href="#" class="report-link" data-report="current-stock">
                                {% if CURRENT_LANGUAGE == 'ar' %}المخزون الحالي{% else %}Current Stock{% endif %}
                            </a>
                        </li>
                        <li class="list-group-item">
                            <a href="#" class="report-link" data-report="low-stock">
                                {% if CURRENT_LANGUAGE == 'ar' %}المنتجات منخفضة المخزون{% else %}Low Stock Products{% endif %}
                            </a>
                        </li>
                        <li class="list-group-item">
                            <a href="#" class="report-link" data-report="expired-products">
                                {% if CURRENT_LANGUAGE == 'ar' %}المنتجات منتهية الصلاحية{% else %}Expired Products{% endif %}
                            </a>
                        </li>
                        <li class="list-group-item">
                            <a href="#" class="report-link" data-report="stock-value">
                                {% if CURRENT_LANGUAGE == 'ar' %}قيمة المخزون{% else %}Stock Value{% endif %}
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-users me-2"></i>
                        {% if CURRENT_LANGUAGE == 'ar' %}تقارير العملاء{% else %}Customer Reports{% endif %}
                    </h5>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item">
                            <a href="#" class="report-link" data-report="top-customers">
                                {% if CURRENT_LANGUAGE == 'ar' %}أفضل العملاء{% else %}Top Customers{% endif %}
                            </a>
                        </li>
                        <li class="list-group-item">
                            <a href="#" class="report-link" data-report="customer-purchases">
                                {% if CURRENT_LANGUAGE == 'ar' %}مشتريات العملاء{% else %}Customer Purchases{% endif %}
                            </a>
                        </li>
                        <li class="list-group-item">
                            <a href="#" class="report-link" data-report="new-customers">
                                {% if CURRENT_LANGUAGE == 'ar' %}العملاء الجدد{% else %}New Customers{% endif %}
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        {% if CURRENT_LANGUAGE == 'ar' %}التقارير المالية{% else %}Financial Reports{% endif %}
                    </h5>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item">
                            <a href="#" class="report-link" data-report="profit-loss">
                                {% if CURRENT_LANGUAGE == 'ar' %}الأرباح والخسائر{% else %}Profit & Loss{% endif %}
                            </a>
                        </li>
                        <li class="list-group-item">
                            <a href="#" class="report-link" data-report="tax-report">
                                {% if CURRENT_LANGUAGE == 'ar' %}تقرير الضرائب{% else %}Tax Report{% endif %}
                            </a>
                        </li>
                        <li class="list-group-item">
                            <a href="#" class="report-link" data-report="payment-methods">
                                {% if CURRENT_LANGUAGE == 'ar' %}طرق الدفع{% else %}Payment Methods{% endif %}
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر التقرير -->
    <div class="card mb-4" id="reportFilters" style="display: none;">
        <div class="card-body">
            <h5 class="card-title mb-3" id="reportTitle">
                {% if CURRENT_LANGUAGE == 'ar' %}تقرير المبيعات اليومية{% else %}Daily Sales Report{% endif %}
            </h5>
            
            <div class="row g-3">
                <div class="col-md-3">
                    <label for="startDate" class="form-label">
                        {% if CURRENT_LANGUAGE == 'ar' %}من تاريخ{% else %}From Date{% endif %}
                    </label>
                    <input type="date" class="form-control" id="startDate">
                </div>
                <div class="col-md-3">
                    <label for="endDate" class="form-label">
                        {% if CURRENT_LANGUAGE == 'ar' %}إلى تاريخ{% else %}To Date{% endif %}
                    </label>
                    <input type="date" class="form-control" id="endDate">
                </div>
                <div class="col-md-3">
                    <label for="filterSelect" class="form-label" id="filterLabel">
                        {% if CURRENT_LANGUAGE == 'ar' %}فلتر{% else %}Filter{% endif %}
                    </label>
                    <select class="form-select" id="filterSelect">
                        <option value="all">
                            {% if CURRENT_LANGUAGE == 'ar' %}الكل{% else %}All{% endif %}
                        </option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button class="btn btn-primary w-100" id="generateReportBtn">
                        {% if CURRENT_LANGUAGE == 'ar' %}إنشاء التقرير{% else %}Generate Report{% endif %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- محتوى التقرير -->
    <div class="card" id="reportContent" style="display: none;">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h5 class="card-title" id="reportContentTitle">
                    {% if CURRENT_LANGUAGE == 'ar' %}تقرير المبيعات اليومية{% else %}Daily Sales Report{% endif %}
                </h5>
                <div>
                    <button class="btn btn-sm btn-outline-primary me-2" id="exportCsvBtn">
                        <i class="fas fa-file-csv me-1"></i>
                        {% if CURRENT_LANGUAGE == 'ar' %}تصدير CSV{% else %}Export CSV{% endif %}
                    </button>
                    <button class="btn btn-sm btn-outline-primary me-2" id="exportPdfBtn">
                        <i class="fas fa-file-pdf me-1"></i>
                        {% if CURRENT_LANGUAGE == 'ar' %}تصدير PDF{% else %}Export PDF{% endif %}
                    </button>
                    <button class="btn btn-sm btn-outline-primary" id="printBtn">
                        <i class="fas fa-print me-1"></i>
                        {% if CURRENT_LANGUAGE == 'ar' %}طباعة{% else %}Print{% endif %}
                    </button>
                </div>
            </div>
            
            <!-- الرسم البياني -->
            <div class="mb-4" id="chartContainer" style="height: 300px;">
                <canvas id="reportChart"></canvas>
            </div>
            
            <!-- جدول البيانات -->
            <div class="table-responsive">
                <table class="table table-striped" id="reportTable">
                    <thead>
                        <tr id="reportTableHeader">
                            <!-- سيتم إضافة العناوين ديناميكياً -->
                            <th>{% if CURRENT_LANGUAGE == 'ar' %}التاريخ{% else %}Date{% endif %}</th>
                            <th>{% if CURRENT_LANGUAGE == 'ar' %}المبيعات{% else %}Sales{% endif %}</th>
                            <th>{% if CURRENT_LANGUAGE == 'ar' %}عدد الفواتير{% else %}Invoices{% endif %}</th>
                            <th>{% if CURRENT_LANGUAGE == 'ar' %}متوسط قيمة الفاتورة{% else %}Avg. Invoice{% endif %}</th>
                        </tr>
                    </thead>
                    <tbody id="reportTableBody">
                        <!-- سيتم إضافة البيانات ديناميكياً -->
                        <tr>
                            <td>2024-05-01</td>
                            <td>5,000.00 ج.م</td>
                            <td>25</td>
                            <td>200.00 ج.م</td>
                        </tr>
                        <tr>
                            <td>2024-05-02</td>
                            <td>6,200.00 ج.م</td>
                            <td>30</td>
                            <td>206.67 ج.م</td>
                        </tr>
                        <tr>
                            <td>2024-05-03</td>
                            <td>4,800.00 ج.م</td>
                            <td>22</td>
                            <td>218.18 ج.م</td>
                        </tr>
                    </tbody>
                    <tfoot id="reportTableFooter">
                        <tr>
                            <th>{% if CURRENT_LANGUAGE == 'ar' %}الإجمالي{% else %}Total{% endif %}</th>
                            <th>16,000.00 ج.م</th>
                            <th>77</th>
                            <th>207.79 ج.م</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentReport = null;
    let reportChart = null;
    
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة الأحداث
        initializeEvents();
    });
    
    // تهيئة الأحداث
    function initializeEvents() {
        // روابط التقارير
        document.querySelectorAll('.report-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const reportType = this.getAttribute('data-report');
                loadReport(reportType);
            });
        });
        
        // زر إنشاء التقرير
        document.getElementById('generateReportBtn').addEventListener('click', function() {
            generateReport();
        });
        
        // زر التحديث
        document.getElementById('refreshBtn').addEventListener('click', function() {
            if (currentReport) {
                generateReport();
            }
        });
        
        // زر الطباعة
        document.getElementById('printBtn').addEventListener('click', function() {
            window.print();
        });
        
        // زر تصدير CSV
        document.getElementById('exportCsvBtn').addEventListener('click', function() {
            exportTableToCSV('reportTable', `${currentReport}_report.csv`);
        });
        
        // زر تصدير PDF
        document.getElementById('exportPdfBtn').addEventListener('click', function() {
            alert('سيتم تصدير التقرير كملف PDF');
        });
    }
    
    // تحميل التقرير
    function loadReport(reportType) {
        currentReport = reportType;
        
        // تحديث عنوان التقرير
        const reportTitles = {
            'daily-sales': 'تقرير المبيعات اليومية',
            'monthly-sales': 'تقرير المبيعات الشهرية',
            'sales-by-product': 'تقرير المبيعات حسب المنتج',
            'sales-by-category': 'تقرير المبيعات حسب الفئة',
            'current-stock': 'تقرير المخزون الحالي',
            'low-stock': 'تقرير المنتجات منخفضة المخزون',
            'expired-products': 'تقرير المنتجات منتهية الصلاحية',
            'stock-value': 'تقرير قيمة المخزون',
            'top-customers': 'تقرير أفضل العملاء',
            'customer-purchases': 'تقرير مشتريات العملاء',
            'new-customers': 'تقرير العملاء الجدد',
            'profit-loss': 'تقرير الأرباح والخسائر',
            'tax-report': 'تقرير الضرائب',
            'payment-methods': 'تقرير طرق الدفع'
        };
        
        document.getElementById('reportTitle').textContent = reportTitles[reportType] || reportType;
        document.getElementById('reportContentTitle').textContent = reportTitles[reportType] || reportType;
        
        // تحديث الفلاتر حسب نوع التقرير
        updateFilters(reportType);
        
        // إظهار قسم الفلاتر
        document.getElementById('reportFilters').style.display = 'block';
        
        // توليد التقرير
        generateReport();
    }
    
    // تحديث الفلاتر حسب نوع التقرير
    function updateFilters(reportType) {
        const filterSelect = document.getElementById('filterSelect');
        const filterLabel = document.getElementById('filterLabel');
        
        // تفريغ القائمة
        filterSelect.innerHTML = '<option value="all">الكل</option>';
        
        // تحديث الفلاتر حسب نوع التقرير
        if (reportType === 'sales-by-product' || reportType === 'sales-by-category') {
            filterLabel.textContent = 'الفئة';
            
            // إضافة الفئات
            const categories = [
                { id: 1, name: 'أدوية' },
                { id: 2, name: 'مستحضرات تجميل' },
                { id: 3, name: 'مكملات غذائية' },
                { id: 4, name: 'أدوات طبية' }
            ];
            
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                filterSelect.appendChild(option);
            });
        } else if (reportType === 'customer-purchases') {
            filterLabel.textContent = 'العميل';
            
            // إضافة العملاء
            const customers = [
                { id: 1, name: 'أحمد محمد' },
                { id: 2, name: 'سارة أحمد' }
            ];
            
            customers.forEach(customer => {
                const option = document.createElement('option');
                option.value = customer.id;
                option.textContent = customer.name;
                filterSelect.appendChild(option);
            });
        } else {
            filterLabel.textContent = 'فلتر';
        }
    }
    
    // توليد التقرير
    function generateReport() {
        // إظهار قسم محتوى التقرير
        document.getElementById('reportContent').style.display = 'block';
        
        // في الإصدار النهائي، سنقوم بجلب البيانات من الخادم
        
        // إنشاء الرسم البياني
        createChart();
    }
    
    // إنشاء الرسم البياني
    function createChart() {
        const ctx = document.getElementById('reportChart').getContext('2d');
        
        // تدمير الرسم البياني السابق إذا وجد
        if (reportChart) {
            reportChart.destroy();
        }
        
        // بيانات تجريبية
        const data = {
            labels: ['2024-05-01', '2024-05-02', '2024-05-03', '2024-05-04', '2024-05-05', '2024-05-06', '2024-05-07'],
            datasets: [{
                label: 'المبيعات (ج.م)',
                data: [5000, 6200, 4800, 7500, 5300, 6800, 7200],
                backgroundColor: 'rgba(13, 110, 253, 0.2)',
                borderColor: 'rgba(13, 110, 253, 1)',
                borderWidth: 2,
                tension: 0.4,
                fill: true
            }]
        };
        
        // إنشاء الرسم البياني
        reportChart = new Chart(ctx, {
            type: 'line',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString('ar-EG') + ' ج.م';
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                }
            }
        });
    }
    
    // تصدير الجدول إلى CSV
    function exportTableToCSV(tableId, filename) {
        const table = document.getElementById(tableId);
        const rows = table.querySelectorAll('tr');
        const csvContent = [];
        
        rows.forEach(row => {
            const cols = row.querySelectorAll('td, th');
            const rowData = [];
            cols.forEach(col => {
                rowData.push('"' + col.textContent.trim().replace(/"/g, '""') + '"');
            });
            csvContent.push(rowData.join(','));
        });
        
        const csvString = csvContent.join('\n');
        const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();
    }
</script>
{% endblock %}
