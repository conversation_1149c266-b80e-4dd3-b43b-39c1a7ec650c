#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مسارات إدارة المخزون
Inventory Management Routes
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from sqlalchemy import and_, or_
from models import Product, Category
from app import db

bp = Blueprint('inventory', __name__, url_prefix='/inventory')

@bp.route('/')
@login_required
def index():
    """صفحة إدارة المخزون الرئيسية"""
    return render_template('inventory/index.html')
