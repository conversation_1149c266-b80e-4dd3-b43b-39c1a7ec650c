لي الملف اللي #!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الصيدليات الاحترافي
Professional Pharmacy Management System

تم التطوير بواسطة: فريق تطوير نظام إدارة الصيدليات
الإصدار: 2.0.0
تاريخ التحديث: 2024

المميزات:
- نقطة بيع احترافية مع دعم الباركود
- إدارة شاملة للمخزون والأدوية
- نظام تقارير متقدم مع رسوم بيانية
- إدارة العملاء والموردين
- دعم متعدد اللغات (العربية والإنجليزية)
- واجهة مستخدم احترافية ومتجاوبة
- نظام صلاحيات متقدم
"""

from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date
import os

# إنشاء التطبيق
app = Flask(__name__)

# الإعدادات الأساسية
app.config['SECRET_KEY'] = 'pharmacy-management-secret-key-2024-professional'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///pharmacy_management.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# تهيئة قاعدة البيانات
db = SQLAlchemy(app)

# قائمة المستخدمين (في الإصدار النهائي ستكون في قاعدة البيانات)
users = {
    'admin': {
        'password': 'admin123',
        'full_name_ar': 'المدير العام',
        'full_name_en': 'System Administrator',
        'role': 'admin'
    },
    'manager': {
        'password': 'manager123',
        'full_name_ar': 'مدير الصيدلية',
        'full_name_en': 'Pharmacy Manager',
        'role': 'manager'
    },
    'cashier': {
        'password': 'cashier123',
        'full_name_ar': 'الكاشير',
        'full_name_en': 'Cashier',
        'role': 'cashier'
    }
}

# ===============================
# المسارات الأساسية
# Basic Routes
# ===============================

@app.route('/')
def index():
    """الصفحة الرئيسية - توجيه للوحة التحكم أو تسجيل الدخول"""
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    if 'user_id' in session:
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        if username in users and users[username]['password'] == password:
            session['user_id'] = username
            session['user_role'] = users[username]['role']
            session['user_name'] = users[username]['full_name_ar']
            flash('تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template('auth/login.html',
                          CURRENT_LANGUAGE='ar',
                          LANGUAGES={'ar': 'العربية', 'en': 'English'},
                          APP_NAME='نظام إدارة الصيدليات',
                          APP_NAME_EN='Pharmacy Management System')

@app.route('/logout')
def logout():
    """تسجيل الخروج"""
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
def dashboard():
    """لوحة التحكم الرئيسية"""
    if 'user_id' not in session:
        return redirect(url_for('login'))

    # بيانات تجريبية للوحة التحكم
    stats = {
        'total_products': 150,
        'low_stock_count': 12,
        'expired_count': 5,
        'today_sales': 5000.0,
        'month_sales': 45000.0,
        'year_sales': 350000.0,
        'total_customers': 85,
        'total_suppliers': 15,
        'suspended_invoices': 3,
        'inventory_value': 250000.0
    }

    # منتجات منخفضة المخزون
    low_stock_products = [
        {'name_ar': 'باراسيتامول', 'stock_quantity': 5, 'min_stock_level': 10},
        {'name_ar': 'أموكسيسيلين', 'stock_quantity': 3, 'min_stock_level': 10},
        {'name_ar': 'فيتامين سي', 'stock_quantity': 8, 'min_stock_level': 15}
    ]

    # منتجات منتهية الصلاحية
    expired_products = [
        {'name_ar': 'أسبرين', 'expiry_date': datetime(2024, 4, 30), 'stock_quantity': 12},
        {'name_ar': 'ديكلوفيناك', 'expiry_date': datetime(2024, 5, 15), 'stock_quantity': 8}
    ]

    # آخر المبيعات
    recent_sales = [
        {'invoice_number': 'INV-20240501-0001', 'customer': {'name_ar': 'أحمد محمد'}, 'total_amount': 500.0, 'sale_date': datetime(2024, 5, 1, 10, 30), 'user': {'full_name_ar': 'المدير العام'}},
        {'invoice_number': 'INV-20240501-0002', 'customer': {'name_ar': 'سارة أحمد'}, 'total_amount': 750.0, 'sale_date': datetime(2024, 5, 1, 14, 15), 'user': {'full_name_ar': 'المدير العام'}},
        {'invoice_number': 'INV-20240430-0005', 'customer': None, 'total_amount': 320.0, 'sale_date': datetime(2024, 4, 30, 16, 45), 'user': {'full_name_ar': 'المدير العام'}}
    ]

    return render_template('dashboard/index.html',
                          current_user={'is_authenticated': True, 'username': session['user_id'], 'full_name_ar': session['user_name'], 'role': session['user_role']},
                          CURRENT_LANGUAGE='ar',
                          LANGUAGES={'ar': 'العربية', 'en': 'English'},
                          APP_NAME='نظام إدارة الصيدليات',
                          APP_NAME_EN='Pharmacy Management System',
                          stats=stats,
                          low_stock_products=low_stock_products,
                          expired_products=expired_products,
                          recent_sales=recent_sales,
                          suspended_sales=3,
                          request={'endpoint': 'dashboard.index'})

# ===============================
# مسارات نقطة البيع
# POS Routes
# ===============================

@app.route('/pos')
def pos():
    """نقطة البيع"""
    if 'user_id' not in session:
        return redirect(url_for('login'))

    # فواتير معلقة تجريبية
    suspended_sales = [
        {'id': 1, 'invoice_number': 'INV-20240501-0003', 'customer': {'name_ar': 'محمد علي'}, 'total_amount': 450.0, 'created_at': datetime(2024, 5, 1, 11, 30)},
        {'id': 2, 'invoice_number': 'INV-20240501-0004', 'customer': None, 'total_amount': 280.0, 'created_at': datetime(2024, 5, 1, 15, 45)}
    ]

    return render_template('pos/index.html',
                          current_user={'is_authenticated': True, 'username': session['user_id'], 'full_name_ar': session['user_name'], 'role': session['user_role']},
                          CURRENT_LANGUAGE='ar',
                          LANGUAGES={'ar': 'العربية', 'en': 'English'},
                          APP_NAME='نظام إدارة الصيدليات',
                          APP_NAME_EN='Pharmacy Management System',
                          suspended_sales=suspended_sales,
                          request={'endpoint': 'pos.index'})

# ===============================
# مسارات إدارة المخزون
# Inventory Routes
# ===============================

@app.route('/inventory')
def inventory():
    """إدارة المخزون"""
    if 'user_id' not in session:
        return redirect(url_for('login'))

    return render_template('inventory/index.html',
                          current_user={'is_authenticated': True, 'username': session['user_id'], 'full_name_ar': session['user_name'], 'role': session['user_role']},
                          CURRENT_LANGUAGE='ar',
                          LANGUAGES={'ar': 'العربية', 'en': 'English'},
                          APP_NAME='نظام إدارة الصيدليات',
                          APP_NAME_EN='Pharmacy Management System',
                          request={'endpoint': 'inventory.index'})

# ===============================
# مسارات المبيعات
# Sales Routes
# ===============================

@app.route('/sales')
def sales():
    """المبيعات"""
    if 'user_id' not in session:
        return redirect(url_for('login'))

    return render_template('sales/index.html',
                          current_user={'is_authenticated': True, 'username': session['user_id'], 'full_name_ar': session['user_name'], 'role': session['user_role']},
                          CURRENT_LANGUAGE='ar',
                          LANGUAGES={'ar': 'العربية', 'en': 'English'},
                          APP_NAME='نظام إدارة الصيدليات',
                          APP_NAME_EN='Pharmacy Management System',
                          request={'endpoint': 'sales.index'})

# ===============================
# مسارات المشتريات
# Purchases Routes
# ===============================

@app.route('/purchases')
def purchases():
    """المشتريات"""
    if 'user_id' not in session:
        return redirect(url_for('login'))

    return render_template('purchases/index.html',
                          current_user={'is_authenticated': True, 'username': session['user_id'], 'full_name_ar': session['user_name'], 'role': session['user_role']},
                          CURRENT_LANGUAGE='ar',
                          LANGUAGES={'ar': 'العربية', 'en': 'English'},
                          APP_NAME='نظام إدارة الصيدليات',
                          APP_NAME_EN='Pharmacy Management System',
                          request={'endpoint': 'purchases.index'})

# ===============================
# مسارات العملاء والموردين
# Customers & Suppliers Routes
# ===============================

@app.route('/customers')
def customers():
    """إدارة العملاء"""
    if 'user_id' not in session:
        return redirect(url_for('login'))

    return render_template('customers/index.html',
                          current_user={'is_authenticated': True, 'username': session['user_id'], 'full_name_ar': session['user_name'], 'role': session['user_role']},
                          CURRENT_LANGUAGE='ar',
                          LANGUAGES={'ar': 'العربية', 'en': 'English'},
                          APP_NAME='نظام إدارة الصيدليات',
                          APP_NAME_EN='Pharmacy Management System',
                          request={'endpoint': 'customers.index'})

@app.route('/suppliers')
def suppliers():
    """إدارة الموردين"""
    if 'user_id' not in session:
        return redirect(url_for('login'))

    return render_template('suppliers/index.html',
                          current_user={'is_authenticated': True, 'username': session['user_id'], 'full_name_ar': session['user_name'], 'role': session['user_role']},
                          CURRENT_LANGUAGE='ar',
                          LANGUAGES={'ar': 'العربية', 'en': 'English'},
                          APP_NAME='نظام إدارة الصيدليات',
                          APP_NAME_EN='Pharmacy Management System',
                          request={'endpoint': 'suppliers.index'})

# ===============================
# مسارات التقارير
# Reports Routes
# ===============================

@app.route('/reports')
def reports():
    """التقارير"""
    if 'user_id' not in session:
        return redirect(url_for('login'))

    return render_template('reports/index.html',
                          current_user={'is_authenticated': True, 'username': session['user_id'], 'full_name_ar': session['user_name'], 'role': session['user_role']},
                          CURRENT_LANGUAGE='ar',
                          LANGUAGES={'ar': 'العربية', 'en': 'English'},
                          APP_NAME='نظام إدارة الصيدليات',
                          APP_NAME_EN='Pharmacy Management System',
                          request={'endpoint': 'reports.index'})

# ===============================
# مسارات الإعدادات وإدارة المستخدمين
# Settings & User Management Routes
# ===============================

@app.route('/settings')
def settings():
    """الإعدادات"""
    if 'user_id' not in session:
        return redirect(url_for('login'))

    return render_template('settings/index.html',
                          current_user={'is_authenticated': True, 'username': session['user_id'], 'full_name_ar': session['user_name'], 'role': session['user_role']},
                          CURRENT_LANGUAGE='ar',
                          LANGUAGES={'ar': 'العربية', 'en': 'English'},
                          APP_NAME='نظام إدارة الصيدليات',
                          APP_NAME_EN='Pharmacy Management System',
                          request={'endpoint': 'settings.index'})

# ===============================
# مسارات إضافية
# Additional Routes
# ===============================

@app.route('/set_language/<language>')
def set_language(language):
    """تبديل اللغة"""
    session['language'] = language
    return redirect(request.referrer or url_for('index'))

@app.route('/api/search_product/<barcode>')
def search_product_by_barcode(barcode):
    """البحث عن منتج بالباركود - API"""
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    # بيانات تجريبية للمنتجات
    products = {
        '123456789': {
            'id': 1,
            'name_ar': 'باراسيتامول',
            'name_en': 'Paracetamol',
            'selling_price': 15.00,
            'stock_quantity': 50,
            'unit': 'قطعة'
        },
        '987654321': {
            'id': 2,
            'name_ar': 'أموكسيسيلين',
            'name_en': 'Amoxicillin',
            'selling_price': 25.00,
            'stock_quantity': 30,
            'unit': 'علبة'
        }
    }

    if barcode in products:
        return jsonify(products[barcode])
    else:
        return jsonify({'error': 'المنتج غير موجود'}), 404

@app.route('/api/dashboard_stats')
def dashboard_stats():
    """إحصائيات لوحة التحكم - API"""
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    stats = {
        'today_sales': 5000.0,
        'month_sales': 45000.0,
        'year_sales': 350000.0,
        'total_products': 150,
        'low_stock_count': 12,
        'expired_count': 5,
        'total_customers': 85,
        'total_suppliers': 15
    }

    return jsonify(stats)

# ===============================
# معالجة الأخطاء
# Error Handlers
# ===============================

@app.errorhandler(404)
def not_found_error(error):
    """صفحة الخطأ 404"""
    return render_template('errors/404.html',
                          CURRENT_LANGUAGE='ar',
                          APP_NAME='نظام إدارة الصيدليات',
                          APP_NAME_EN='Pharmacy Management System'), 404

@app.errorhandler(500)
def internal_error(error):
    """صفحة الخطأ 500"""
    return render_template('errors/500.html',
                          CURRENT_LANGUAGE='ar',
                          APP_NAME='نظام إدارة الصيدليات',
                          APP_NAME_EN='Pharmacy Management System'), 500

# ===============================
# فلاتر القوالب
# Template Filters
# ===============================

@app.template_filter('currency')
def currency_filter(amount):
    """تنسيق العملة"""
    return f"{amount:,.2f} ج.م"

@app.template_filter('datetime')
def datetime_filter(dt):
    """تنسيق التاريخ والوقت"""
    if dt:
        return dt.strftime('%Y-%m-%d %H:%M')
    return ''

@app.template_filter('date')
def date_filter(dt):
    """تنسيق التاريخ"""
    if dt:
        return dt.strftime('%Y-%m-%d')
    return ''

# ===============================
# السياق العام للقوالب
# Template Context
# ===============================

@app.context_processor
def inject_globals():
    """حقن المتغيرات العامة في جميع القوالب"""
    return {
        'current_year': datetime.now().year,
        'app_version': '2.0.0',
        'company_name': 'نظام إدارة الصيدليات الاحترافي'
    }

# ===============================
# تشغيل التطبيق
# Run Application
# ===============================

if __name__ == '__main__':
    print("=" * 60)
    print("🏥 نظام إدارة الصيدليات الاحترافي")
    print("   Professional Pharmacy Management System")
    print("=" * 60)
    print("📋 الإصدار: 2.0.0")
    print("🌐 الخادم: http://localhost:5000")
    print("👤 المستخدمون المتاحون:")
    print("   - admin / admin123 (مدير النظام)")
    print("   - manager / manager123 (مدير الصيدلية)")
    print("   - cashier / cashier123 (كاشير)")
    print("=" * 60)
    print("🚀 جاري تشغيل الخادم...")
    print("=" * 60)

    # إنشاء الجداول إذا لم تكن موجودة
    with app.app_context():
        try:
            db.create_all()
            print("✅ تم إنشاء قاعدة البيانات بنجاح")
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")

    # تشغيل التطبيق
    try:
        print("🔄 بدء تشغيل الخادم على المنفذ 5000...")
        app.run(
            host='127.0.0.1',
            port=5000,
            debug=False,
            use_reloader=False
        )
    except KeyboardInterrupt:
        print("\n" + "=" * 60)
        print("🛑 تم إيقاف الخادم بواسطة المستخدم")
        print("=" * 60)
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")
        import traceback
        traceback.print_exc()
        print("=" * 60)
