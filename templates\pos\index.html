{% extends "base.html" %}

{% block title %}
    {% if CURRENT_LANGUAGE == 'ar' %}نقطة البيع - {{ APP_NAME }}{% else %}Point of Sale - {{ APP_NAME_EN }}{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    /* تنسيقات خاصة بنقطة البيع */
    .pos-container {
        height: calc(100vh - var(--navbar-height) - 2rem);
        overflow: hidden;
    }
    
    .pos-products {
        height: 100%;
        overflow-y: auto;
    }
    
    .pos-cart {
        height: 100%;
        display: flex;
        flex-direction: column;
    }
    
    .pos-cart-items {
        flex: 1;
        overflow-y: auto;
    }
    
    .pos-cart-totals {
        background-color: var(--gray-100);
        border-top: 1px solid var(--gray-300);
        padding: 1rem;
    }
    
    .pos-product-card {
        cursor: pointer;
        transition: all 0.2s ease;
        height: 100%;
    }
    
    .pos-product-card:hover {
        transform: translateY(-3px);
        box-shadow: var(--box-shadow-lg);
    }
    
    .pos-product-image {
        height: 120px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--gray-100);
    }
    
    .pos-product-image i {
        font-size: 3rem;
        color: var(--gray-500);
    }
    
    .pos-cart-item {
        border-bottom: 1px solid var(--gray-200);
        padding: 0.75rem 0;
    }
    
    .pos-cart-item:last-child {
        border-bottom: none;
    }
    
    .pos-cart-item-price {
        font-weight: 600;
    }
    
    .pos-cart-item-quantity {
        width: 60px;
        text-align: center;
    }
    
    .pos-cart-item-total {
        font-weight: 700;
        color: var(--primary-blue);
    }
    
    .pos-cart-empty {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: var(--gray-500);
    }
    
    .pos-cart-empty i {
        font-size: 4rem;
        margin-bottom: 1rem;
    }
    
    .pos-search-container {
        position: relative;
    }
    
    .pos-search-results {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: white;
        border: 1px solid var(--gray-300);
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow-lg);
        z-index: 1000;
        max-height: 300px;
        overflow-y: auto;
        display: none;
    }
    
    .pos-search-item {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid var(--gray-200);
        cursor: pointer;
    }
    
    .pos-search-item:hover {
        background-color: var(--light-blue);
    }
    
    .pos-search-item:last-child {
        border-bottom: none;
    }
    
    .pos-category-filter {
        overflow-x: auto;
        white-space: nowrap;
        padding-bottom: 0.5rem;
    }
    
    .pos-category-filter .btn {
        margin-right: 0.25rem;
    }
    
    .pos-total-amount {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-blue);
    }
    
    .pos-suspended-invoices {
        max-height: 300px;
        overflow-y: auto;
    }
    
    /* تنسيقات للوحدات المتعددة */
    .unit-selector {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .unit-selector .btn {
        flex: 1;
        min-width: 80px;
    }
    
    /* تنسيقات للشاشات الصغيرة */
    @media (max-width: 992px) {
        .pos-container {
            height: auto;
        }
        
        .pos-products, .pos-cart {
            height: auto;
            max-height: 500px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h1 class="h3 mb-0">
            {% if CURRENT_LANGUAGE == 'ar' %}نقطة البيع{% else %}Point of Sale{% endif %}
        </h1>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-primary" id="suspendInvoiceBtn">
                <i class="fas fa-pause-circle"></i>
                {% if CURRENT_LANGUAGE == 'ar' %}تعليق الفاتورة{% else %}Suspend Invoice{% endif %}
            </button>
            <button class="btn btn-outline-success" id="showSuspendedBtn">
                <i class="fas fa-list"></i>
                {% if CURRENT_LANGUAGE == 'ar' %}الفواتير المعلقة{% else %}Suspended Invoices{% endif %}
                {% if suspended_sales %}
                <span class="badge bg-danger">{{ suspended_sales|length }}</span>
                {% endif %}
            </button>
            <button class="btn btn-primary" id="newInvoiceBtn">
                <i class="fas fa-plus"></i>
                {% if CURRENT_LANGUAGE == 'ar' %}فاتورة جديدة{% else %}New Invoice{% endif %}
            </button>
        </div>
    </div>

    <!-- نقطة البيع الرئيسية -->
    <div class="row pos-container">
        <!-- الجانب الأيمن - المنتجات -->
        <div class="col-lg-8 pos-products">
            <!-- البحث والفلترة -->
            <div class="card mb-3">
                <div class="card-body">
                    <div class="row g-3">
                        <!-- البحث -->
                        <div class="col-md-8">
                            <div class="pos-search-container">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="productSearch" placeholder="{% if CURRENT_LANGUAGE == 'ar' %}بحث عن منتج (اسم، باركود){% else %}Search products (name, barcode){% endif %}">
                                    <button class="btn btn-outline-secondary" type="button" id="barcodeBtn">
                                        <i class="fas fa-barcode"></i>
                                    </button>
                                </div>
                                <div class="pos-search-results" id="searchResults"></div>
                            </div>
                        </div>
                        
                        <!-- العميل -->
                        <div class="col-md-4">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-user"></i>
                                </span>
                                <input type="text" class="form-control" id="customerSearch" placeholder="{% if CURRENT_LANGUAGE == 'ar' %}بحث عن عميل{% else %}Search customer{% endif %}">
                                <button class="btn btn-outline-secondary" type="button" id="addCustomerBtn">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                            <div class="pos-search-results" id="customerResults"></div>
                        </div>
                    </div>
                    
                    <!-- فلتر الفئات -->
                    <div class="pos-category-filter mt-3" id="categoryFilter">
                        <button class="btn btn-sm btn-primary active" data-category="all">
                            {% if CURRENT_LANGUAGE == 'ar' %}الكل{% else %}All{% endif %}
                        </button>
                        <!-- سيتم إضافة الفئات ديناميكياً -->
                    </div>
                </div>
            </div>
            
            <!-- عرض المنتجات -->
            <div class="row row-cols-2 row-cols-md-3 row-cols-lg-4 row-cols-xl-5 g-3" id="productsContainer">
                <!-- سيتم إضافة المنتجات ديناميكياً -->
                <div class="col">
                    <div class="card pos-product-card">
                        <div class="pos-product-image">
                            <i class="fas fa-pills"></i>
                        </div>
                        <div class="card-body">
                            <h6 class="card-title">اسم المنتج</h6>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-primary fw-bold">50.00 ج.م</span>
                                <span class="badge bg-success">متوفر</span>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- نهاية المنتج النموذجي -->
            </div>
        </div>
        
        <!-- الجانب الأيسر - سلة المشتريات -->
        <div class="col-lg-4 pos-cart">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-shopping-cart me-2"></i>
                            {% if CURRENT_LANGUAGE == 'ar' %}الفاتورة{% else %}Invoice{% endif %}
                        </h5>
                        <span id="invoiceNumber">
                            {% if CURRENT_LANGUAGE == 'ar' %}جديد{% else %}New{% endif %}
                        </span>
                    </div>
                </div>
                
                <!-- عناصر السلة -->
                <div class="pos-cart-items" id="cartItems">
                    <div class="pos-cart-empty">
                        <i class="fas fa-shopping-cart"></i>
                        <h5>{% if CURRENT_LANGUAGE == 'ar' %}السلة فارغة{% else %}Cart is empty{% endif %}</h5>
                        <p>{% if CURRENT_LANGUAGE == 'ar' %}أضف منتجات للفاتورة{% else %}Add products to invoice{% endif %}</p>
                    </div>
                </div>
                
                <!-- المجاميع -->
                <div class="pos-cart-totals">
                    <div class="row mb-2">
                        <div class="col-6">{% if CURRENT_LANGUAGE == 'ar' %}المجموع الفرعي:{% else %}Subtotal:{% endif %}</div>
                        <div class="col-6 text-end" id="subtotal">0.00 ج.م</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-6">{% if CURRENT_LANGUAGE == 'ar' %}الخصم:{% else %}Discount:{% endif %}</div>
                        <div class="col-6 text-end">
                            <div class="input-group input-group-sm">
                                <input type="number" class="form-control" id="discountPercentage" value="0" min="0" max="100">
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-6">{% if CURRENT_LANGUAGE == 'ar' %}قيمة الخصم:{% else %}Discount Amount:{% endif %}</div>
                        <div class="col-6 text-end" id="discountAmount">0.00 ج.م</div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-6">{% if CURRENT_LANGUAGE == 'ar' %}الضريبة (14%):{% else %}Tax (14%):{% endif %}</div>
                        <div class="col-6 text-end" id="taxAmount">0.00 ج.م</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-6"><strong>{% if CURRENT_LANGUAGE == 'ar' %}الإجمالي:{% else %}Total:{% endif %}</strong></div>
                        <div class="col-6 text-end pos-total-amount" id="totalAmount">0.00 ج.م</div>
                    </div>
                    
                    <!-- طرق الدفع -->
                    <div class="mb-3">
                        <div class="btn-group w-100" role="group">
                            <input type="radio" class="btn-check" name="paymentMethod" id="paymentCash" value="cash" checked>
                            <label class="btn btn-outline-primary" for="paymentCash">
                                <i class="fas fa-money-bill-wave"></i> {% if CURRENT_LANGUAGE == 'ar' %}نقداً{% else %}Cash{% endif %}
                            </label>
                            
                            <input type="radio" class="btn-check" name="paymentMethod" id="paymentCard" value="card">
                            <label class="btn btn-outline-primary" for="paymentCard">
                                <i class="fas fa-credit-card"></i> {% if CURRENT_LANGUAGE == 'ar' %}بطاقة{% else %}Card{% endif %}
                            </label>
                            
                            <input type="radio" class="btn-check" name="paymentMethod" id="paymentCredit" value="credit">
                            <label class="btn btn-outline-primary" for="paymentCredit">
                                <i class="fas fa-handshake"></i> {% if CURRENT_LANGUAGE == 'ar' %}آجل{% else %}Credit{% endif %}
                            </label>
                        </div>
                    </div>
                    
                    <!-- أزرار الإجراءات -->
                    <div class="d-grid gap-2">
                        <button class="btn btn-success btn-lg" id="checkoutBtn">
                            <i class="fas fa-check-circle me-2"></i>
                            {% if CURRENT_LANGUAGE == 'ar' %}إتمام البيع{% else %}Complete Sale{% endif %}
                        </button>
                        <button class="btn btn-danger" id="cancelBtn">
                            <i class="fas fa-times-circle me-2"></i>
                            {% if CURRENT_LANGUAGE == 'ar' %}إلغاء{% else %}Cancel{% endif %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة تفاصيل المنتج -->
<div class="modal fade" id="productModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="productModalTitle">تفاصيل المنتج</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">{% if CURRENT_LANGUAGE == 'ar' %}الكمية:{% else %}Quantity:{% endif %}</label>
                    <input type="number" class="form-control" id="productQuantity" value="1" min="1">
                </div>
                
                <div class="mb-3">
                    <label class="form-label">{% if CURRENT_LANGUAGE == 'ar' %}وحدة البيع:{% else %}Sale Unit:{% endif %}</label>
                    <div class="unit-selector" id="unitSelector">
                        <!-- سيتم إضافة وحدات البيع ديناميكياً -->
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">{% if CURRENT_LANGUAGE == 'ar' %}السعر:{% else %}Price:{% endif %}</label>
                    <div class="input-group">
                        <input type="number" class="form-control" id="productPrice" step="0.01">
                        <span class="input-group-text">ج.م</span>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">{% if CURRENT_LANGUAGE == 'ar' %}خصم على المنتج:{% else %}Item Discount:{% endif %}</label>
                    <div class="input-group">
                        <input type="number" class="form-control" id="productDiscount" value="0" min="0" step="0.01">
                        <span class="input-group-text">ج.م</span>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">{% if CURRENT_LANGUAGE == 'ar' %}الإجمالي:{% else %}Total:{% endif %}</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="productTotal" readonly>
                        <span class="input-group-text">ج.م</span>
                    </div>
                </div>
                
                <div class="alert alert-info" id="productStock">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if CURRENT_LANGUAGE == 'ar' %}المخزون المتاح: {% else %}Available Stock: {% endif %}
                    <span id="availableStock">0</span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {% if CURRENT_LANGUAGE == 'ar' %}إلغاء{% else %}Cancel{% endif %}
                </button>
                <button type="button" class="btn btn-primary" id="addToCartBtn">
                    {% if CURRENT_LANGUAGE == 'ar' %}إضافة للسلة{% else %}Add to Cart{% endif %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة الفواتير المعلقة -->
<div class="modal fade" id="suspendedInvoicesModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    {% if CURRENT_LANGUAGE == 'ar' %}الفواتير المعلقة{% else %}Suspended Invoices{% endif %}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="pos-suspended-invoices">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if CURRENT_LANGUAGE == 'ar' %}رقم الفاتورة{% else %}Invoice No.{% endif %}</th>
                                <th>{% if CURRENT_LANGUAGE == 'ar' %}العميل{% else %}Customer{% endif %}</th>
                                <th>{% if CURRENT_LANGUAGE == 'ar' %}المبلغ{% else %}Amount{% endif %}</th>
                                <th>{% if CURRENT_LANGUAGE == 'ar' %}التاريخ{% else %}Date{% endif %}</th>
                                <th>{% if CURRENT_LANGUAGE == 'ar' %}الإجراءات{% else %}Actions{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody id="suspendedInvoicesList">
                            {% for sale in suspended_sales %}
                            <tr>
                                <td>{{ sale.invoice_number }}</td>
                                <td>{{ sale.customer.name_ar if sale.customer else 'عميل نقدي' }}</td>
                                <td>{{ "%.2f"|format(sale.total_amount) }} ج.م</td>
                                <td>{{ sale.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    <button class="btn btn-sm btn-primary resume-invoice" data-id="{{ sale.id }}">
                                        <i class="fas fa-play"></i>
                                        {% if CURRENT_LANGUAGE == 'ar' %}استكمال{% else %}Resume{% endif %}
                                    </button>
                                    <button class="btn btn-sm btn-danger delete-invoice" data-id="{{ sale.id }}">
                                        <i class="fas fa-trash"></i>
                                        {% if CURRENT_LANGUAGE == 'ar' %}حذف{% else %}Delete{% endif %}
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {% if CURRENT_LANGUAGE == 'ar' %}إغلاق{% else %}Close{% endif %}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/pos.js') }}"></script>
{% endblock %}
