#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مسارات إدارة المشتريات
Purchase Management Routes
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from models import Purchase, PurchaseItem
from app import db

bp = Blueprint('purchases', __name__, url_prefix='/purchases')

@bp.route('/')
@login_required
def index():
    """صفحة إدارة المشتريات الرئيسية"""
    return render_template('purchases/index.html')
