#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مسارات الإعدادات
Settings Routes
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from models import User
from app import db

bp = Blueprint('settings', __name__, url_prefix='/settings')

@bp.route('/')
@login_required
def index():
    """صفحة الإعدادات الرئيسية"""
    if not current_user.has_permission('settings'):
        flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
        return redirect(url_for('dashboard.index'))
    
    return render_template('settings/index.html')
