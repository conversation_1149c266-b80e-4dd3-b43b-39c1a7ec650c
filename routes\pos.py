#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مسارات نقطة البيع
Point of Sale Routes

تحتوي على مسارات نقطة البيع الاحترافية
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for, session
from flask_login import login_required, current_user
from sqlalchemy import and_, or_
from datetime import datetime, date
from models import Product, Sale, SaleItem, Customer
from app import db
import uuid

bp = Blueprint('pos', __name__, url_prefix='/pos')

@bp.route('/')
@login_required
def index():
    """نقطة البيع الرئيسية"""
    
    # الحصول على الفواتير المعلقة
    suspended_sales = Sale.query.filter_by(
        is_suspended=True,
        user_id=current_user.id
    ).order_by(Sale.created_at.desc()).all()
    
    return render_template('pos/index.html', suspended_sales=suspended_sales)

@bp.route('/api/search_products')
@login_required
def api_search_products():
    """البحث عن المنتجات"""
    query = request.args.get('q', '').strip()
    
    if len(query) < 2:
        return jsonify([])
    
    # البحث في اسم المنتج أو الباركود
    products = Product.query.filter(
        and_(
            Product.is_active == True,
            or_(
                Product.name_ar.contains(query),
                Product.name_en.contains(query),
                Product.barcode.contains(query)
            )
        )
    ).limit(20).all()
    
    results = []
    for product in products:
        sale_units = product.get_sale_units()
        results.append({
            'id': product.id,
            'name_ar': product.name_ar,
            'name_en': product.name_en,
            'barcode': product.barcode,
            'price': float(product.price),
            'stock_quantity': product.stock_quantity,
            'sale_units': sale_units,
            'category': product.category.name_ar if product.category else None
        })
    
    return jsonify(results)

@bp.route('/api/get_product/<int:product_id>')
@login_required
def api_get_product(product_id):
    """الحصول على تفاصيل منتج"""
    product = Product.query.get_or_404(product_id)
    
    if not product.is_active:
        return jsonify({'error': 'المنتج غير نشط'}), 400
    
    sale_units = product.get_sale_units()
    
    return jsonify({
        'id': product.id,
        'name_ar': product.name_ar,
        'name_en': product.name_en,
        'barcode': product.barcode,
        'price': float(product.price),
        'stock_quantity': product.stock_quantity,
        'sale_units': sale_units,
        'category': product.category.name_ar if product.category else None
    })

@bp.route('/api/search_customers')
@login_required
def api_search_customers():
    """البحث عن العملاء"""
    query = request.args.get('q', '').strip()
    
    if len(query) < 2:
        return jsonify([])
    
    customers = Customer.query.filter(
        and_(
            Customer.is_active == True,
            or_(
                Customer.name_ar.contains(query),
                Customer.phone.contains(query),
                Customer.national_id.contains(query)
            )
        )
    ).limit(10).all()
    
    results = []
    for customer in customers:
        results.append({
            'id': customer.id,
            'name_ar': customer.name_ar,
            'phone': customer.phone,
            'total_purchases': float(customer.total_purchases)
        })
    
    return jsonify(results)

@bp.route('/api/create_sale', methods=['POST'])
@login_required
def api_create_sale():
    """إنشاء فاتورة جديدة"""
    try:
        data = request.get_json()
        
        # التحقق من البيانات المطلوبة
        items = data.get('items', [])
        if not items:
            return jsonify({'error': 'لا توجد عناصر في الفاتورة'}), 400
        
        # إنشاء رقم فاتورة جديد
        invoice_number = generate_invoice_number()
        
        # إنشاء الفاتورة
        sale = Sale(
            invoice_number=invoice_number,
            customer_id=data.get('customer_id'),
            user_id=current_user.id,
            discount_percentage=float(data.get('discount_percentage', 0)),
            tax_percentage=float(data.get('tax_percentage', 14)),
            payment_method=data.get('payment_method', 'cash'),
            notes_ar=data.get('notes', ''),
            is_suspended=data.get('is_suspended', False)
        )
        
        db.session.add(sale)
        db.session.flush()  # للحصول على معرف الفاتورة
        
        # إضافة عناصر الفاتورة
        total_amount = 0
        for item_data in items:
            product = Product.query.get(item_data['product_id'])
            if not product or not product.is_active:
                return jsonify({'error': f'المنتج غير موجود أو غير نشط'}), 400
            
            quantity = int(item_data['quantity'])
            unit_type = item_data.get('unit_type', 'piece')
            unit_factor = int(item_data.get('unit_factor', 1))
            unit_price = float(item_data['unit_price'])
            discount_amount = float(item_data.get('discount_amount', 0))
            
            # التحقق من توفر الكمية
            required_stock = quantity * unit_factor
            if product.stock_quantity < required_stock:
                return jsonify({'error': f'الكمية المطلوبة غير متوفرة للمنتج {product.name_ar}'}), 400
            
            # إنشاء عنصر الفاتورة
            sale_item = SaleItem(
                sale_id=sale.id,
                product_id=product.id,
                quantity=quantity,
                unit_type=unit_type,
                unit_factor=unit_factor,
                unit_price=unit_price,
                discount_amount=discount_amount
            )
            sale_item.calculate_total()
            
            db.session.add(sale_item)
            
            # تحديث المخزون إذا لم تكن الفاتورة معلقة
            if not sale.is_suspended:
                product.stock_quantity -= required_stock
        
        # حساب مجاميع الفاتورة
        sale.calculate_totals()
        
        # حفظ التغييرات
        db.session.commit()
        
        return jsonify({
            'success': True,
            'sale_id': sale.id,
            'invoice_number': sale.invoice_number,
            'total_amount': float(sale.total_amount)
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'حدث خطأ: {str(e)}'}), 500

@bp.route('/api/suspend_sale', methods=['POST'])
@login_required
def api_suspend_sale():
    """تعليق فاتورة"""
    try:
        data = request.get_json()
        sale_id = data.get('sale_id')
        
        sale = Sale.query.get_or_404(sale_id)
        
        if sale.user_id != current_user.id:
            return jsonify({'error': 'غير مسموح'}), 403
        
        sale.is_suspended = True
        db.session.commit()
        
        return jsonify({'success': True})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'حدث خطأ: {str(e)}'}), 500

@bp.route('/api/resume_sale/<int:sale_id>')
@login_required
def api_resume_sale(sale_id):
    """استكمال فاتورة معلقة"""
    sale = Sale.query.get_or_404(sale_id)
    
    if sale.user_id != current_user.id:
        return jsonify({'error': 'غير مسموح'}), 403
    
    if not sale.is_suspended:
        return jsonify({'error': 'الفاتورة غير معلقة'}), 400
    
    # إرجاع بيانات الفاتورة
    items = []
    for item in sale.items:
        items.append({
            'product_id': item.product_id,
            'product_name': item.product.name_ar,
            'quantity': item.quantity,
            'unit_type': item.unit_type,
            'unit_factor': item.unit_factor,
            'unit_price': float(item.unit_price),
            'discount_amount': float(item.discount_amount),
            'total_price': float(item.total_price)
        })
    
    return jsonify({
        'sale_id': sale.id,
        'invoice_number': sale.invoice_number,
        'customer_id': sale.customer_id,
        'discount_percentage': float(sale.discount_percentage),
        'tax_percentage': float(sale.tax_percentage),
        'notes': sale.notes_ar,
        'items': items,
        'total_amount': float(sale.total_amount)
    })

@bp.route('/api/delete_suspended_sale/<int:sale_id>', methods=['DELETE'])
@login_required
def api_delete_suspended_sale(sale_id):
    """حذف فاتورة معلقة"""
    try:
        sale = Sale.query.get_or_404(sale_id)
        
        if sale.user_id != current_user.id:
            return jsonify({'error': 'غير مسموح'}), 403
        
        if not sale.is_suspended:
            return jsonify({'error': 'لا يمكن حذف فاتورة غير معلقة'}), 400
        
        db.session.delete(sale)
        db.session.commit()
        
        return jsonify({'success': True})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'حدث خطأ: {str(e)}'}), 500

@bp.route('/print/<int:sale_id>')
@login_required
def print_invoice(sale_id):
    """طباعة الفاتورة"""
    sale = Sale.query.get_or_404(sale_id)
    
    return render_template('pos/print_invoice.html', sale=sale)

def generate_invoice_number():
    """توليد رقم فاتورة جديد"""
    today = date.today()
    prefix = f"INV-{today.strftime('%Y%m%d')}"
    
    # البحث عن آخر فاتورة اليوم
    last_sale = Sale.query.filter(
        Sale.invoice_number.like(f"{prefix}%")
    ).order_by(Sale.invoice_number.desc()).first()
    
    if last_sale:
        # استخراج الرقم التسلسلي
        last_number = int(last_sale.invoice_number.split('-')[-1])
        new_number = last_number + 1
    else:
        new_number = 1
    
    return f"{prefix}-{new_number:04d}"
