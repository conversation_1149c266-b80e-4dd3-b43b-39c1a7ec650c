// نظام إدارة الصيدليات - JavaScript الرئيسي
// Pharmacy Management System - Main JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة التطبيق
    initializeApp();
    
    // تهيئة الوضع المظلم
    initializeTheme();
    
    // تهيئة التنبيهات
    initializeAlerts();
    
    // تهيئة الجداول
    initializeTables();
    
    // تهيئة النماذج
    initializeForms();
});

// تهيئة التطبيق
function initializeApp() {
    console.log('تم تحميل نظام إدارة الصيدليات');
    
    // إخفاء شاشة التحميل إذا كانت موجودة
    const loader = document.querySelector('.loader');
    if (loader) {
        setTimeout(() => {
            loader.style.display = 'none';
        }, 1000);
    }
    
    // تفعيل التلميحات
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // تفعيل النوافذ المنبثقة
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

// تهيئة الوضع المظلم
function initializeTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    applyTheme(savedTheme);
    
    // تحديث أيقونة الوضع
    updateThemeIcon(savedTheme);
}

// تطبيق الوضع
function applyTheme(theme) {
    document.body.classList.toggle('dark-theme', theme === 'dark');
    localStorage.setItem('theme', theme);
}

// تبديل الوضع
function toggleTheme() {
    const currentTheme = localStorage.getItem('theme') || 'light';
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    
    applyTheme(newTheme);
    updateThemeIcon(newTheme);
    
    // إرسال طلب لحفظ الوضع في الخادم
    fetch('/api/save-theme', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ theme: newTheme })
    });
}

// تحديث أيقونة الوضع
function updateThemeIcon(theme) {
    const themeIcon = document.getElementById('theme-icon');
    if (themeIcon) {
        themeIcon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
    }
}

// تهيئة التنبيهات
function initializeAlerts() {
    // إخفاء التنبيهات تلقائياً بعد 5 ثوانٍ
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
}

// تهيئة الجداول
function initializeTables() {
    // إضافة فئات CSS للجداول
    const tables = document.querySelectorAll('table');
    tables.forEach(table => {
        if (!table.classList.contains('table')) {
            table.classList.add('table', 'table-striped', 'table-hover');
        }
    });
    
    // تفعيل الترتيب للجداول
    initializeTableSorting();
}

// تهيئة ترتيب الجداول
function initializeTableSorting() {
    const sortableHeaders = document.querySelectorAll('th[data-sortable]');
    sortableHeaders.forEach(header => {
        header.style.cursor = 'pointer';
        header.addEventListener('click', function() {
            sortTable(this);
        });
        
        // إضافة أيقونة الترتيب
        if (!header.querySelector('.sort-icon')) {
            const icon = document.createElement('i');
            icon.className = 'fas fa-sort sort-icon ms-2';
            header.appendChild(icon);
        }
    });
}

// ترتيب الجدول
function sortTable(header) {
    const table = header.closest('table');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const columnIndex = Array.from(header.parentNode.children).indexOf(header);
    const isAscending = header.classList.contains('sort-asc');
    
    // إزالة فئات الترتيب من جميع العناوين
    table.querySelectorAll('th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
        const icon = th.querySelector('.sort-icon');
        if (icon) {
            icon.className = 'fas fa-sort sort-icon ms-2';
        }
    });
    
    // ترتيب الصفوف
    rows.sort((a, b) => {
        const aValue = a.children[columnIndex].textContent.trim();
        const bValue = b.children[columnIndex].textContent.trim();
        
        // التحقق من كون القيم أرقام
        const aNum = parseFloat(aValue.replace(/[^\d.-]/g, ''));
        const bNum = parseFloat(bValue.replace(/[^\d.-]/g, ''));
        
        if (!isNaN(aNum) && !isNaN(bNum)) {
            return isAscending ? bNum - aNum : aNum - bNum;
        } else {
            return isAscending ? 
                bValue.localeCompare(aValue, 'ar') : 
                aValue.localeCompare(bValue, 'ar');
        }
    });
    
    // إعادة ترتيب الصفوف في الجدول
    rows.forEach(row => tbody.appendChild(row));
    
    // تحديث فئة الترتيب والأيقونة
    header.classList.add(isAscending ? 'sort-desc' : 'sort-asc');
    const icon = header.querySelector('.sort-icon');
    if (icon) {
        icon.className = `fas fa-sort-${isAscending ? 'down' : 'up'} sort-icon ms-2`;
    }
}

// تهيئة النماذج
function initializeForms() {
    // تفعيل التحقق من صحة النماذج
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
    
    // تنسيق حقول الأرقام
    const numberInputs = document.querySelectorAll('input[type="number"]');
    numberInputs.forEach(input => {
        input.addEventListener('input', function() {
            formatNumberInput(this);
        });
    });
    
    // تنسيق حقول العملة
    const currencyInputs = document.querySelectorAll('.currency-input');
    currencyInputs.forEach(input => {
        input.addEventListener('input', function() {
            formatCurrencyInput(this);
        });
    });
}

// تنسيق حقل الأرقام
function formatNumberInput(input) {
    let value = input.value.replace(/[^\d.-]/g, '');
    if (value !== input.value) {
        input.value = value;
    }
}

// تنسيق حقل العملة
function formatCurrencyInput(input) {
    let value = input.value.replace(/[^\d.]/g, '');
    if (value) {
        const number = parseFloat(value);
        if (!isNaN(number)) {
            input.value = number.toFixed(2);
        }
    }
}

// عرض رسالة تأكيد
function showConfirmDialog(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

// عرض رسالة نجاح
function showSuccessMessage(message) {
    showAlert(message, 'success');
}

// عرض رسالة خطأ
function showErrorMessage(message) {
    showAlert(message, 'danger');
}

// عرض رسالة تحذير
function showWarningMessage(message) {
    showAlert(message, 'warning');
}

// عرض تنبيه
function showAlert(message, type = 'info') {
    const alertContainer = document.querySelector('.flash-messages') || document.body;
    
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.appendChild(alert);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوانٍ
    setTimeout(() => {
        const bsAlert = new bootstrap.Alert(alert);
        bsAlert.close();
    }, 5000);
}

// تحميل البيانات عبر AJAX
function loadData(url, callback, errorCallback) {
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (callback) callback(data);
        })
        .catch(error => {
            console.error('Error loading data:', error);
            if (errorCallback) {
                errorCallback(error);
            } else {
                showErrorMessage('حدث خطأ في تحميل البيانات');
            }
        });
}

// إرسال البيانات عبر AJAX
function sendData(url, data, callback, errorCallback) {
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
    })
    .then(result => {
        if (callback) callback(result);
    })
    .catch(error => {
        console.error('Error sending data:', error);
        if (errorCallback) {
            errorCallback(error);
        } else {
            showErrorMessage('حدث خطأ في إرسال البيانات');
        }
    });
}

// تنسيق العملة
function formatCurrency(amount) {
    if (amount === null || amount === undefined) {
        amount = 0;
    }
    return new Intl.NumberFormat('ar-EG', {
        style: 'currency',
        currency: 'EGP',
        minimumFractionDigits: 2
    }).format(amount);
}

// تنسيق الأرقام
function formatNumber(number) {
    if (number === null || number === undefined) {
        number = 0;
    }
    return new Intl.NumberFormat('ar-EG').format(number);
}

// تنسيق التاريخ
function formatDate(date, locale = 'ar-EG') {
    if (!date) return '';
    
    const dateObj = new Date(date);
    return new Intl.DateTimeFormat(locale, {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    }).format(dateObj);
}

// تنسيق الوقت
function formatDateTime(date, locale = 'ar-EG') {
    if (!date) return '';
    
    const dateObj = new Date(date);
    return new Intl.DateTimeFormat(locale, {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    }).format(dateObj);
}

// تصدير الجدول إلى CSV
function exportTableToCSV(tableId, filename = 'data.csv') {
    const table = document.getElementById(tableId);
    if (!table) return;
    
    const rows = table.querySelectorAll('tr');
    const csvContent = [];
    
    rows.forEach(row => {
        const cols = row.querySelectorAll('td, th');
        const rowData = [];
        cols.forEach(col => {
            rowData.push('"' + col.textContent.trim().replace(/"/g, '""') + '"');
        });
        csvContent.push(rowData.join(','));
    });
    
    const csvString = csvContent.join('\n');
    const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    link.click();
}

// طباعة الصفحة
function printPage() {
    window.print();
}

// تبديل الشريط الجانبي في الشاشات الصغيرة
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
        sidebar.classList.toggle('show');
    }
}
