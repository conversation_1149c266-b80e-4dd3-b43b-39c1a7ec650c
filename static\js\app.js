/**
 * نظام إدارة الصيدليات - JavaScript الرئيسي
 * Pharmacy Management System - Main JavaScript
 *
 * تم التطوير بواسطة: فريق تطوير نظام إدارة الصيدليات
 * الإصدار: 2.0.0
 * تاريخ التحديث: 2024
 */

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // عرض رسالة ترحيب في وحدة التحكم
    console.log('%c نظام إدارة الصيدليات - الإصدار 2.0.0 ', 'background: linear-gradient(135deg, #0d6efd, #dc3545); color: white; padding: 10px; border-radius: 5px; font-size: 14px; font-weight: bold;');

    // تهيئة التطبيق
    initializeApp();

    // تهيئة الوضع المظلم
    initializeTheme();

    // تهيئة التنبيهات
    initializeAlerts();

    // تهيئة الجداول
    initializeTables();

    // تهيئة النماذج
    initializeForms();

    // تهيئة الرسوم البيانية
    initializeCharts();

    // تهيئة تأثيرات الظهور
    initializeAnimations();

    // تهيئة القائمة الجانبية للأجهزة المحمولة
    initializeMobileSidebar();
});

/**
 * تهيئة التطبيق
 * Initialize Application
 */
function initializeApp() {
    // إخفاء شاشة التحميل بتأثير متلاشي
    const loader = document.querySelector('.loader');
    if (loader) {
        loader.classList.add('fade-out');
        setTimeout(() => {
            loader.style.display = 'none';
        }, 1000);
    }

    // تفعيل التلميحات مع تأثيرات متقدمة
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl, {
            animation: true,
            delay: { show: 100, hide: 100 },
            html: true
        });
    });

    // تفعيل النوافذ المنبثقة مع تأثيرات متقدمة
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl, {
            animation: true,
            html: true,
            trigger: 'hover focus'
        });
    });
}

// تهيئة الوضع المظلم
function initializeTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    applyTheme(savedTheme);

    // تحديث أيقونة الوضع
    updateThemeIcon(savedTheme);
}

// تطبيق الوضع
function applyTheme(theme) {
    document.body.classList.toggle('dark-theme', theme === 'dark');
    localStorage.setItem('theme', theme);
}

// تبديل الوضع
function toggleTheme() {
    const currentTheme = localStorage.getItem('theme') || 'light';
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';

    applyTheme(newTheme);
    updateThemeIcon(newTheme);

    // إرسال طلب لحفظ الوضع في الخادم
    fetch('/api/save-theme', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ theme: newTheme })
    });
}

// تحديث أيقونة الوضع
function updateThemeIcon(theme) {
    const themeIcon = document.getElementById('theme-icon');
    if (themeIcon) {
        themeIcon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
    }
}

// تهيئة التنبيهات
function initializeAlerts() {
    // إخفاء التنبيهات تلقائياً بعد 5 ثوانٍ
    const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
    alerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
}

// تهيئة الجداول
function initializeTables() {
    // إضافة فئات CSS للجداول
    const tables = document.querySelectorAll('table');
    tables.forEach(table => {
        if (!table.classList.contains('table')) {
            table.classList.add('table', 'table-striped', 'table-hover');
        }
    });

    // تفعيل الترتيب للجداول
    initializeTableSorting();
}

// تهيئة ترتيب الجداول
function initializeTableSorting() {
    const sortableHeaders = document.querySelectorAll('th[data-sortable]');
    sortableHeaders.forEach(header => {
        header.style.cursor = 'pointer';
        header.addEventListener('click', function() {
            sortTable(this);
        });

        // إضافة أيقونة الترتيب
        if (!header.querySelector('.sort-icon')) {
            const icon = document.createElement('i');
            icon.className = 'fas fa-sort sort-icon ms-2';
            header.appendChild(icon);
        }
    });
}

// ترتيب الجدول
function sortTable(header) {
    const table = header.closest('table');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const columnIndex = Array.from(header.parentNode.children).indexOf(header);
    const isAscending = header.classList.contains('sort-asc');

    // إزالة فئات الترتيب من جميع العناوين
    table.querySelectorAll('th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
        const icon = th.querySelector('.sort-icon');
        if (icon) {
            icon.className = 'fas fa-sort sort-icon ms-2';
        }
    });

    // ترتيب الصفوف
    rows.sort((a, b) => {
        const aValue = a.children[columnIndex].textContent.trim();
        const bValue = b.children[columnIndex].textContent.trim();

        // التحقق من كون القيم أرقام
        const aNum = parseFloat(aValue.replace(/[^\d.-]/g, ''));
        const bNum = parseFloat(bValue.replace(/[^\d.-]/g, ''));

        if (!isNaN(aNum) && !isNaN(bNum)) {
            return isAscending ? bNum - aNum : aNum - bNum;
        } else {
            return isAscending ?
                bValue.localeCompare(aValue, 'ar') :
                aValue.localeCompare(bValue, 'ar');
        }
    });

    // إعادة ترتيب الصفوف في الجدول
    rows.forEach(row => tbody.appendChild(row));

    // تحديث فئة الترتيب والأيقونة
    header.classList.add(isAscending ? 'sort-desc' : 'sort-asc');
    const icon = header.querySelector('.sort-icon');
    if (icon) {
        icon.className = `fas fa-sort-${isAscending ? 'down' : 'up'} sort-icon ms-2`;
    }
}

// تهيئة النماذج
function initializeForms() {
    // تفعيل التحقق من صحة النماذج
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });

    // تنسيق حقول الأرقام
    const numberInputs = document.querySelectorAll('input[type="number"]');
    numberInputs.forEach(input => {
        input.addEventListener('input', function() {
            formatNumberInput(this);
        });
    });

    // تنسيق حقول العملة
    const currencyInputs = document.querySelectorAll('.currency-input');
    currencyInputs.forEach(input => {
        input.addEventListener('input', function() {
            formatCurrencyInput(this);
        });
    });
}

// تنسيق حقل الأرقام
function formatNumberInput(input) {
    let value = input.value.replace(/[^\d.-]/g, '');
    if (value !== input.value) {
        input.value = value;
    }
}

// تنسيق حقل العملة
function formatCurrencyInput(input) {
    let value = input.value.replace(/[^\d.]/g, '');
    if (value) {
        const number = parseFloat(value);
        if (!isNaN(number)) {
            input.value = number.toFixed(2);
        }
    }
}

// عرض رسالة تأكيد
function showConfirmDialog(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

// عرض رسالة نجاح
function showSuccessMessage(message) {
    showAlert(message, 'success');
}

// عرض رسالة خطأ
function showErrorMessage(message) {
    showAlert(message, 'danger');
}

// عرض رسالة تحذير
function showWarningMessage(message) {
    showAlert(message, 'warning');
}

// عرض تنبيه
function showAlert(message, type = 'info') {
    const alertContainer = document.querySelector('.flash-messages') || document.body;

    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    alertContainer.appendChild(alert);

    // إزالة التنبيه تلقائياً بعد 5 ثوانٍ
    setTimeout(() => {
        const bsAlert = new bootstrap.Alert(alert);
        bsAlert.close();
    }, 5000);
}

// تحميل البيانات عبر AJAX
function loadData(url, callback, errorCallback) {
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (callback) callback(data);
        })
        .catch(error => {
            console.error('Error loading data:', error);
            if (errorCallback) {
                errorCallback(error);
            } else {
                showErrorMessage('حدث خطأ في تحميل البيانات');
            }
        });
}

// إرسال البيانات عبر AJAX
function sendData(url, data, callback, errorCallback) {
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.json();
    })
    .then(result => {
        if (callback) callback(result);
    })
    .catch(error => {
        console.error('Error sending data:', error);
        if (errorCallback) {
            errorCallback(error);
        } else {
            showErrorMessage('حدث خطأ في إرسال البيانات');
        }
    });
}

// تنسيق العملة
function formatCurrency(amount) {
    if (amount === null || amount === undefined) {
        amount = 0;
    }
    return new Intl.NumberFormat('ar-EG', {
        style: 'currency',
        currency: 'EGP',
        minimumFractionDigits: 2
    }).format(amount);
}

// تنسيق الأرقام
function formatNumber(number) {
    if (number === null || number === undefined) {
        number = 0;
    }
    return new Intl.NumberFormat('ar-EG').format(number);
}

// تنسيق التاريخ
function formatDate(date, locale = 'ar-EG') {
    if (!date) return '';

    const dateObj = new Date(date);
    return new Intl.DateTimeFormat(locale, {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    }).format(dateObj);
}

// تنسيق الوقت
function formatDateTime(date, locale = 'ar-EG') {
    if (!date) return '';

    const dateObj = new Date(date);
    return new Intl.DateTimeFormat(locale, {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    }).format(dateObj);
}

/**
 * تهيئة الرسوم البيانية
 * Initialize Charts
 */
function initializeCharts() {
    // رسم بياني للمبيعات
    const salesChartEl = document.getElementById('salesChart');
    if (salesChartEl) {
        const ctx = salesChartEl.getContext('2d');

        // بيانات تجريبية للمبيعات
        const salesData = {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو'],
            datasets: [{
                label: 'المبيعات',
                data: [12000, 19000, 15000, 25000, 22000, 30000, 28000],
                backgroundColor: 'rgba(13, 110, 253, 0.2)',
                borderColor: 'rgba(13, 110, 253, 1)',
                borderWidth: 2,
                tension: 0.4,
                fill: true
            }, {
                label: 'المشتريات',
                data: [8000, 12000, 10000, 18000, 15000, 20000, 18000],
                backgroundColor: 'rgba(220, 53, 69, 0.2)',
                borderColor: 'rgba(220, 53, 69, 1)',
                borderWidth: 2,
                tension: 0.4,
                fill: true
            }]
        };

        const options = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.parsed.y !== null) {
                                label += formatCurrency(context.parsed.y);
                            }
                            return label;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return formatCurrency(value);
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            },
            animation: {
                duration: 2000,
                easing: 'easeOutQuart'
            }
        };

        new Chart(ctx, {
            type: 'line',
            data: salesData,
            options: options
        });
    }

    // رسم بياني للمنتجات الأكثر مبيعاً
    const topProductsChartEl = document.getElementById('topProductsChart');
    if (topProductsChartEl) {
        const ctx = topProductsChartEl.getContext('2d');

        // بيانات تجريبية للمنتجات
        const productsData = {
            labels: ['باراسيتامول', 'أموكسيسيلين', 'فيتامين سي', 'أسبرين', 'ديكلوفيناك'],
            datasets: [{
                label: 'المبيعات',
                data: [300, 250, 200, 180, 150],
                backgroundColor: [
                    'rgba(13, 110, 253, 0.8)',
                    'rgba(220, 53, 69, 0.8)',
                    'rgba(25, 135, 84, 0.8)',
                    'rgba(255, 193, 7, 0.8)',
                    'rgba(13, 202, 240, 0.8)'
                ],
                borderColor: [
                    'rgba(13, 110, 253, 1)',
                    'rgba(220, 53, 69, 1)',
                    'rgba(25, 135, 84, 1)',
                    'rgba(255, 193, 7, 1)',
                    'rgba(13, 202, 240, 1)'
                ],
                borderWidth: 1,
                hoverOffset: 15
            }]
        };

        const options = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            let label = context.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.parsed !== null) {
                                label += formatNumber(context.parsed) + ' وحدة';
                            }
                            return label;
                        }
                    }
                }
            },
            animation: {
                animateRotate: true,
                animateScale: true,
                duration: 2000,
                easing: 'easeOutQuart'
            }
        };

        new Chart(ctx, {
            type: 'doughnut',
            data: productsData,
            options: options
        });
    }
}

/**
 * تهيئة تأثيرات الظهور
 * Initialize Animations
 */
function initializeAnimations() {
    // تأثير ظهور البطاقات
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
        card.style.transitionDelay = (index * 0.1) + 's';

        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100);
    });

    // تأثير ظهور الإحصائيات
    const statValues = document.querySelectorAll('.stat-value');
    statValues.forEach((statValue) => {
        const finalValue = parseInt(statValue.textContent.replace(/,/g, ''));
        animateCounter(statValue, 0, finalValue, 2000);
    });
}

/**
 * تحريك العداد
 * Animate Counter
 */
function animateCounter(element, start, end, duration) {
    let startTimestamp = null;
    const step = (timestamp) => {
        if (!startTimestamp) startTimestamp = timestamp;
        const progress = Math.min((timestamp - startTimestamp) / duration, 1);
        const currentValue = Math.floor(progress * (end - start) + start);
        element.textContent = formatNumber(currentValue);
        if (progress < 1) {
            window.requestAnimationFrame(step);
        }
    };
    window.requestAnimationFrame(step);
}

/**
 * تهيئة القائمة الجانبية للأجهزة المحمولة
 * Initialize Mobile Sidebar
 */
function initializeMobileSidebar() {
    const navbarToggler = document.querySelector('.navbar-toggler');
    const sidebar = document.querySelector('.sidebar');

    if (navbarToggler && sidebar) {
        navbarToggler.addEventListener('click', function() {
            sidebar.classList.toggle('show');
            document.body.classList.toggle('sidebar-open');
        });

        // إغلاق القائمة الجانبية عند النقر خارجها
        document.addEventListener('click', function(event) {
            const isClickInsideNavbar = navbarToggler.contains(event.target);
            const isClickInsideSidebar = sidebar.contains(event.target);

            if (!isClickInsideNavbar && !isClickInsideSidebar && sidebar.classList.contains('show')) {
                sidebar.classList.remove('show');
                document.body.classList.remove('sidebar-open');
            }
        });
    }
}

// تصدير الجدول إلى CSV
function exportTableToCSV(tableId, filename = 'data.csv') {
    const table = document.getElementById(tableId);
    if (!table) return;

    // عرض رسالة تحميل
    showAlert('جاري تحضير ملف التصدير...', 'info');

    const rows = table.querySelectorAll('tr');
    const csvContent = [];

    rows.forEach(row => {
        const cols = row.querySelectorAll('td, th');
        const rowData = [];
        cols.forEach(col => {
            rowData.push('"' + col.textContent.trim().replace(/"/g, '""') + '"');
        });
        csvContent.push(rowData.join(','));
    });

    const csvString = csvContent.join('\n');
    const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });

    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    link.click();

    // عرض رسالة نجاح
    setTimeout(() => {
        showSuccessMessage('تم تصدير البيانات بنجاح');
    }, 1000);
}

// طباعة الصفحة
function printPage() {
    // عرض رسالة تحميل
    showAlert('جاري تحضير الصفحة للطباعة...', 'info');

    setTimeout(() => {
        window.print();
    }, 500);
}

// تبديل الشريط الجانبي في الشاشات الصغيرة
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
        sidebar.classList.toggle('show');
        document.body.classList.toggle('sidebar-open');
    }
}
