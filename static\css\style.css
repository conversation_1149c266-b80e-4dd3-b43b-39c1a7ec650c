/* نظام إدارة الصيدليات - التصميم الرئيسي */
/* Pharmacy Management System - Main Styles */

/* ========== متغيرات الألوان ========== */
:root {
  /* الألوان الأساسية - الأحمر والأزرق */
  --primary-red: #dc3545;
  --primary-blue: #0d6efd;
  --secondary-red: #c82333;
  --secondary-blue: #0b5ed7;
  --light-red: #f8d7da;
  --light-blue: #cfe2ff;
  --dark-red: #721c24;
  --dark-blue: #052c65;

  /* الألوان المساعدة */
  --success: #198754;
  --warning: #ffc107;
  --danger: #dc3545;
  --info: #0dcaf0;
  --light: #f8f9fa;
  --dark: #212529;
  --white: #ffffff;
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-400: #ced4da;
  --gray-500: #adb5bd;
  --gray-600: #6c757d;
  --gray-700: #495057;
  --gray-800: #343a40;
  --gray-900: #212529;

  /* الخطوط */
  --font-arabic: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-english: 'Roboto', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

  /* المسافات */
  --sidebar-width: 280px;
  --navbar-height: 60px;
  --border-radius: 8px;
  --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* ========== الإعدادات العامة ========== */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-arabic);
  background-color: var(--gray-100);
  margin: 0;
  padding: 0;
  line-height: 1.6;
}

[dir="ltr"] body {
  font-family: var(--font-english);
}

/* ========== شريط التنقل العلوي ========== */
.navbar {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-red) 100%) !important;
  box-shadow: var(--box-shadow-lg);
  height: var(--navbar-height);
  z-index: 1030;
}

.navbar-brand {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--white) !important;
}

.navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500;
  transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
  color: var(--white) !important;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius);
}

.dropdown-menu {
  border: none;
  box-shadow: var(--box-shadow-lg);
  border-radius: var(--border-radius);
}

/* ========== الشريط الجانبي ========== */
.main-wrapper {
  display: flex;
  margin-top: var(--navbar-height);
  min-height: calc(100vh - var(--navbar-height));
}

.sidebar {
  width: var(--sidebar-width);
  background: linear-gradient(180deg, var(--dark-blue) 0%, var(--dark-red) 100%);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: var(--box-shadow-lg);
  position: fixed;
  top: var(--navbar-height);
  bottom: 0;
  overflow-y: auto;
  z-index: 1020;
  transition: all 0.3s ease;
  color: var(--white);
}

[dir="rtl"] .sidebar {
  border-right: none;
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  right: 0;
}

.sidebar-content {
  padding: 0;
}

/* Brand section */
.sidebar-brand {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  background: rgba(0, 0, 0, 0.2);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.brand-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-red) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
}

[dir="rtl"] .brand-icon {
  margin-right: 0;
  margin-left: 1rem;
}

.brand-icon i {
  font-size: 1.5rem;
  color: var(--white);
}

.brand-text {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--white);
}

/* User section */
.sidebar-user {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.user-avatar {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
}

[dir="rtl"] .user-avatar {
  margin-right: 0;
  margin-left: 1rem;
}

.user-avatar i {
  font-size: 1.2rem;
  color: var(--white);
}

.user-info {
  flex: 1;
}

.user-name {
  font-weight: 600;
  color: var(--white);
  font-size: 0.9rem;
}

.user-role {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
}

/* Menu sections */
.menu-section {
  margin-bottom: 1rem;
}

.menu-header {
  padding: 0.75rem 1.5rem;
  font-size: 0.8rem;
  text-transform: uppercase;
  color: rgba(255, 255, 255, 0.5);
  font-weight: 600;
  letter-spacing: 1px;
}

.sidebar .nav-link {
  color: rgba(255, 255, 255, 0.8);
  padding: 0.75rem 1.5rem;
  margin: 0.1rem 0;
  border-radius: 0;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  text-decoration: none;
  border-left: 3px solid transparent;
}

[dir="rtl"] .sidebar .nav-link {
  border-left: none;
  border-right: 3px solid transparent;
}

.sidebar .nav-link i {
  width: 20px;
  margin-right: 0.75rem;
  font-size: 1.1rem;
}

[dir="rtl"] .sidebar .nav-link i {
  margin-right: 0;
  margin-left: 0.75rem;
}

.sidebar .nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--white);
  border-left-color: var(--primary-red);
}

[dir="rtl"] .sidebar .nav-link:hover {
  border-left-color: transparent;
  border-right-color: var(--primary-red);
}

.sidebar .nav-link.active {
  background: rgba(255, 255, 255, 0.15);
  color: var(--white);
  border-left-color: var(--primary-red);
  box-shadow: none;
}

[dir="rtl"] .sidebar .nav-link.active {
  border-left-color: transparent;
  border-right-color: var(--primary-red);
}

/* ========== المحتوى الرئيسي ========== */
.main-content {
  flex: 1;
  margin-left: var(--sidebar-width);
  padding: 2rem;
  background-color: var(--gray-100);
  min-height: calc(100vh - var(--navbar-height));
}

[dir="rtl"] .main-content {
  margin-left: 0;
  margin-right: var(--sidebar-width);
}

/* ========== البطاقات ========== */
.card {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  transition: all 0.3s ease;
  background-color: var(--white);
}

.card:hover {
  box-shadow: var(--box-shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  background: linear-gradient(135deg, var(--light-blue) 0%, var(--light-red) 100%);
  border-bottom: 1px solid var(--gray-300);
  font-weight: 600;
  color: var(--dark);
}

/* ========== الأزرار ========== */
.btn {
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-red) 100%);
  border: none;
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--secondary-blue) 0%, var(--secondary-red) 100%);
  transform: translateY(-1px);
  box-shadow: var(--box-shadow-lg);
}

.btn-outline-primary {
  border: 2px solid var(--primary-blue);
  color: var(--primary-blue);
}

.btn-outline-primary:hover {
  background: var(--primary-blue);
  border-color: var(--primary-blue);
}

.btn-success {
  background-color: var(--success);
}

.btn-warning {
  background-color: var(--warning);
  color: var(--dark);
}

.btn-danger {
  background-color: var(--danger);
}

/* ========== النماذج ========== */
.form-control {
  border: 2px solid var(--gray-300);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: 0.5rem;
}

/* ========== الجداول ========== */
.table {
  background-color: var(--white);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

.table thead th {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-red) 100%);
  color: var(--white);
  border: none;
  font-weight: 600;
  padding: 1rem;
}

.table tbody tr {
  transition: all 0.3s ease;
}

.table tbody tr:hover {
  background-color: var(--light-blue);
}

.table td {
  padding: 0.75rem 1rem;
  border-top: 1px solid var(--gray-200);
}

/* ========== رسائل التنبيه ========== */
.flash-messages {
  margin-bottom: 1rem;
}

.alert {
  border: none;
  border-radius: var(--border-radius);
  font-weight: 500;
}

.alert-success {
  background-color: var(--light);
  color: var(--success);
  border-left: 4px solid var(--success);
}

.alert-danger {
  background-color: var(--light-red);
  color: var(--danger);
  border-left: 4px solid var(--danger);
}

.alert-warning {
  background-color: #fff3cd;
  color: var(--warning);
  border-left: 4px solid var(--warning);
}

.alert-info {
  background-color: #d1ecf1;
  color: var(--info);
  border-left: 4px solid var(--info);
}

/* ========== بطاقات الإحصائيات ========== */
.stat-card {
  background: linear-gradient(135deg, var(--white) 0%, var(--gray-100) 100%);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
  transition: all 0.3s ease;
  border-left: 4px solid var(--primary-blue);
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--box-shadow-lg);
}

.stat-card.danger {
  border-left-color: var(--danger);
}

.stat-card.success {
  border-left-color: var(--success);
}

.stat-card.warning {
  border-left-color: var(--warning);
}

.stat-card .stat-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: var(--primary-blue);
}

.stat-card.danger .stat-icon {
  color: var(--danger);
}

.stat-card.success .stat-icon {
  color: var(--success);
}

.stat-card.warning .stat-icon {
  color: var(--warning);
}

.stat-card .stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--dark);
  margin-bottom: 0.5rem;
}

.stat-card .stat-label {
  color: var(--gray-600);
  font-weight: 500;
}

/* ========== التصميم المتجاوب ========== */
@media (max-width: 992px) {
  .sidebar {
    transform: translateX(-100%);
  }

  [dir="rtl"] .sidebar {
    transform: translateX(100%);
  }

  .main-content {
    margin-left: 0;
    margin-right: 0;
  }

  .sidebar.show {
    transform: translateX(0);
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 1rem;
  }

  .stat-card {
    margin-bottom: 1rem;
  }
}

/* ========== الرسوم المتحركة ========== */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

/* ========== التمرير المخصص ========== */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-200);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-red) 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--secondary-blue) 0%, var(--secondary-red) 100%);
}
