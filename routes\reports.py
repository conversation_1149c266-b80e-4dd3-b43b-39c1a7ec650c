#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مسارات التقارير
Reports Routes
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from models import Sale, Product, Customer
from app import db

bp = Blueprint('reports', __name__, url_prefix='/reports')

@bp.route('/')
@login_required
def index():
    """صفحة التقارير الرئيسية"""
    return render_template('reports/index.html')
