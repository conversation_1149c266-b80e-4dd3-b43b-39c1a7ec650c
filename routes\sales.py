#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مسارات إدارة المبيعات
Sales Management Routes
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from models import Sale, SaleItem
from app import db

bp = Blueprint('sales', __name__, url_prefix='/sales')

@bp.route('/')
@login_required
def index():
    """صفحة إدارة المبيعات الرئيسية"""
    return render_template('sales/index.html')
