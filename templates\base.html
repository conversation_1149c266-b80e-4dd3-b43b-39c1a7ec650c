<!DOCTYPE html>
<html lang="{{ CURRENT_LANGUAGE }}" dir="{{ 'rtl' if CURRENT_LANGUAGE == 'ar' else 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ APP_NAME }}{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic Support -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    {% if CURRENT_LANGUAGE == 'ar' %}
    <link href="{{ url_for('static', filename='css/rtl.css') }}" rel="stylesheet">
    {% endif %}
    
    {% block extra_css %}{% endblock %}
</head>
<body class="{% if session.get('theme') == 'dark' %}dark-theme{% endif %}">
    
    {% if current_user.is_authenticated %}
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <!-- Brand -->
            <a class="navbar-brand fw-bold" href="{{ url_for('dashboard.index') }}">
                <i class="fas fa-pills me-2"></i>
                {% if CURRENT_LANGUAGE == 'ar' %}
                    {{ APP_NAME }}
                {% else %}
                    {{ APP_NAME_EN }}
                {% endif %}
            </a>
            
            <!-- Toggle button for mobile -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- Navigation items -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <!-- Quick actions -->
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('pos.index') }}">
                            <i class="fas fa-cash-register me-1"></i>
                            {% if CURRENT_LANGUAGE == 'ar' %}نقطة البيع{% else %}POS{% endif %}
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <!-- Language switcher -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-globe me-1"></i>
                            {{ LANGUAGES[CURRENT_LANGUAGE] }}
                        </a>
                        <ul class="dropdown-menu">
                            {% for code, name in LANGUAGES.items() %}
                            <li>
                                <a class="dropdown-item {% if code == CURRENT_LANGUAGE %}active{% endif %}" 
                                   href="{{ url_for('set_language', language=code) }}">
                                    {{ name }}
                                </a>
                            </li>
                            {% endfor %}
                        </ul>
                    </li>
                    
                    <!-- Theme switcher -->
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="toggleTheme()">
                            <i class="fas fa-moon" id="theme-icon"></i>
                        </a>
                    </li>
                    
                    <!-- User menu -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            {{ current_user.full_name_ar if CURRENT_LANGUAGE == 'ar' else current_user.full_name_en or current_user.full_name_ar }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">
                                <i class="fas fa-user-circle me-2"></i>
                                {% if CURRENT_LANGUAGE == 'ar' %}الملف الشخصي{% else %}Profile{% endif %}
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.change_password') }}">
                                <i class="fas fa-key me-2"></i>
                                {% if CURRENT_LANGUAGE == 'ar' %}تغيير كلمة المرور{% else %}Change Password{% endif %}
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                {% if CURRENT_LANGUAGE == 'ar' %}تسجيل الخروج{% else %}Logout{% endif %}
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main content wrapper -->
    <div class="main-wrapper">
        <!-- Sidebar -->
        <nav class="sidebar">
            <div class="sidebar-content">
                <ul class="nav flex-column">
                    <!-- Dashboard -->
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'dashboard.index' %}active{% endif %}" 
                           href="{{ url_for('dashboard.index') }}">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>{% if CURRENT_LANGUAGE == 'ar' %}لوحة التحكم{% else %}Dashboard{% endif %}</span>
                        </a>
                    </li>
                    
                    <!-- POS -->
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint and 'pos' in request.endpoint %}active{% endif %}" 
                           href="{{ url_for('pos.index') }}">
                            <i class="fas fa-cash-register"></i>
                            <span>{% if CURRENT_LANGUAGE == 'ar' %}نقطة البيع{% else %}Point of Sale{% endif %}</span>
                        </a>
                    </li>
                    
                    <!-- Inventory -->
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint and 'inventory' in request.endpoint %}active{% endif %}" 
                           href="{{ url_for('inventory.index') }}">
                            <i class="fas fa-boxes"></i>
                            <span>{% if CURRENT_LANGUAGE == 'ar' %}إدارة المخزون{% else %}Inventory{% endif %}</span>
                        </a>
                    </li>
                    
                    <!-- Sales -->
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint and 'sales' in request.endpoint %}active{% endif %}" 
                           href="{{ url_for('sales.index') }}">
                            <i class="fas fa-shopping-cart"></i>
                            <span>{% if CURRENT_LANGUAGE == 'ar' %}المبيعات{% else %}Sales{% endif %}</span>
                        </a>
                    </li>
                    
                    <!-- Purchases -->
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint and 'purchases' in request.endpoint %}active{% endif %}" 
                           href="{{ url_for('purchases.index') }}">
                            <i class="fas fa-truck"></i>
                            <span>{% if CURRENT_LANGUAGE == 'ar' %}المشتريات{% else %}Purchases{% endif %}</span>
                        </a>
                    </li>
                    
                    <!-- Customers -->
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint and 'customers' in request.endpoint %}active{% endif %}" 
                           href="{{ url_for('customers.index') }}">
                            <i class="fas fa-users"></i>
                            <span>{% if CURRENT_LANGUAGE == 'ar' %}العملاء{% else %}Customers{% endif %}</span>
                        </a>
                    </li>
                    
                    <!-- Suppliers -->
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint and 'suppliers' in request.endpoint %}active{% endif %}" 
                           href="{{ url_for('suppliers.index') }}">
                            <i class="fas fa-industry"></i>
                            <span>{% if CURRENT_LANGUAGE == 'ar' %}الموردين{% else %}Suppliers{% endif %}</span>
                        </a>
                    </li>
                    
                    <!-- Reports -->
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint and 'reports' in request.endpoint %}active{% endif %}" 
                           href="{{ url_for('reports.index') }}">
                            <i class="fas fa-chart-bar"></i>
                            <span>{% if CURRENT_LANGUAGE == 'ar' %}التقارير{% else %}Reports{% endif %}</span>
                        </a>
                    </li>
                    
                    <!-- Settings -->
                    {% if current_user.role in ['admin', 'manager'] %}
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint and 'settings' in request.endpoint %}active{% endif %}" 
                           href="{{ url_for('settings.index') }}">
                            <i class="fas fa-cog"></i>
                            <span>{% if CURRENT_LANGUAGE == 'ar' %}الإعدادات{% else %}Settings{% endif %}</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </nav>
        
        <!-- Main content -->
        <main class="main-content">
            <!-- Flash messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    <div class="flash-messages">
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
            {% endwith %}
            
            <!-- Page content -->
            {% block content %}{% endblock %}
        </main>
    </div>
    {% endif %}
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
