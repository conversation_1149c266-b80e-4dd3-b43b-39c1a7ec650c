{% extends "base.html" %}

{% block title %}
    {% if CURRENT_LANGUAGE == 'ar' %}إدارة العملاء - {{ APP_NAME }}{% else %}Customer Management - {{ APP_NAME_EN }}{% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            {% if CURRENT_LANGUAGE == 'ar' %}إدارة العملاء{% else %}Customer Management{% endif %}
        </h1>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-primary" id="refreshBtn">
                <i class="fas fa-sync-alt"></i>
                {% if CURRENT_LANGUAGE == 'ar' %}تحديث{% else %}Refresh{% endif %}
            </button>
            <button class="btn btn-primary" id="addCustomerBtn" data-bs-toggle="modal" data-bs-target="#customerModal">
                <i class="fas fa-plus"></i>
                {% if CURRENT_LANGUAGE == 'ar' %}إضافة عميل{% else %}Add Customer{% endif %}
            </button>
        </div>
    </div>

    <!-- بطاقات الإحصائيات -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-value" id="total-customers">0</div>
                            <div class="stat-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}إجمالي العملاء{% else %}Total Customers{% endif %}
                            </div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card success">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-value" id="active-customers">0</div>
                            <div class="stat-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}العملاء النشطين{% else %}Active Customers{% endif %}
                            </div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-user-check"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card warning">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-value" id="new-customers">0</div>
                            <div class="stat-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}عملاء جدد (هذا الشهر){% else %}New Customers (This Month){% endif %}
                            </div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-user-plus"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card primary">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-value" id="total-sales">0</div>
                            <div class="stat-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}إجمالي المبيعات (ج.م){% else %}Total Sales (EGP){% endif %}
                            </div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- البحث والفلترة -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="searchInput" placeholder="{% if CURRENT_LANGUAGE == 'ar' %}بحث (الاسم، رقم الهاتف، البريد الإلكتروني){% else %}Search (Name, Phone, Email){% endif %}">
                    </div>
                </div>
                <div class="col-md-4">
                    <select class="form-select" id="statusFilter">
                        <option value="all">{% if CURRENT_LANGUAGE == 'ar' %}جميع العملاء{% else %}All Customers{% endif %}</option>
                        <option value="active">{% if CURRENT_LANGUAGE == 'ar' %}نشط{% else %}Active{% endif %}</option>
                        <option value="inactive">{% if CURRENT_LANGUAGE == 'ar' %}غير نشط{% else %}Inactive{% endif %}</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button class="btn btn-primary w-100" id="searchBtn">
                        {% if CURRENT_LANGUAGE == 'ar' %}بحث{% else %}Search{% endif %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول العملاء -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="customersTable">
                    <thead>
                        <tr>
                            <th>{% if CURRENT_LANGUAGE == 'ar' %}الرقم{% else %}ID{% endif %}</th>
                            <th>{% if CURRENT_LANGUAGE == 'ar' %}الاسم{% else %}Name{% endif %}</th>
                            <th>{% if CURRENT_LANGUAGE == 'ar' %}رقم الهاتف{% else %}Phone{% endif %}</th>
                            <th>{% if CURRENT_LANGUAGE == 'ar' %}البريد الإلكتروني{% else %}Email{% endif %}</th>
                            <th>{% if CURRENT_LANGUAGE == 'ar' %}العنوان{% else %}Address{% endif %}</th>
                            <th>{% if CURRENT_LANGUAGE == 'ar' %}إجمالي المشتريات{% else %}Total Purchases{% endif %}</th>
                            <th>{% if CURRENT_LANGUAGE == 'ar' %}الحالة{% else %}Status{% endif %}</th>
                            <th>{% if CURRENT_LANGUAGE == 'ar' %}الإجراءات{% else %}Actions{% endif %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- سيتم إضافة العملاء ديناميكياً -->
                        <tr>
                            <td>1</td>
                            <td>أحمد محمد</td>
                            <td>01234567890</td>
                            <td><EMAIL></td>
                            <td>القاهرة، مصر</td>
                            <td>1,500.00 ج.م</td>
                            <td><span class="badge bg-success">نشط</span></td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-info view-customer" data-id="1" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-primary edit-customer" data-id="1" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-danger delete-customer" data-id="1" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>سارة أحمد</td>
                            <td>01098765432</td>
                            <td><EMAIL></td>
                            <td>الإسكندرية، مصر</td>
                            <td>2,200.00 ج.م</td>
                            <td><span class="badge bg-success">نشط</span></td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-info view-customer" data-id="2" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-primary edit-customer" data-id="2" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-danger delete-customer" data-id="2" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- ترقيم الصفحات -->
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1">
                            {% if CURRENT_LANGUAGE == 'ar' %}السابق{% else %}Previous{% endif %}
                        </a>
                    </li>
                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                    <li class="page-item">
                        <a class="page-link" href="#">
                            {% if CURRENT_LANGUAGE == 'ar' %}التالي{% else %}Next{% endif %}
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- نافذة إضافة/تعديل عميل -->
<div class="modal fade" id="customerModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="customerModalTitle">
                    {% if CURRENT_LANGUAGE == 'ar' %}إضافة عميل جديد{% else %}Add New Customer{% endif %}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="customerForm" class="needs-validation" novalidate>
                    <input type="hidden" id="customerId" value="">
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="nameAr" class="form-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}الاسم (عربي){% else %}Name (Arabic){% endif %}
                                <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="nameAr" required>
                        </div>
                        <div class="col-md-6">
                            <label for="nameEn" class="form-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}الاسم (إنجليزي){% else %}Name (English){% endif %}
                            </label>
                            <input type="text" class="form-control" id="nameEn">
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="phone" class="form-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}رقم الهاتف{% else %}Phone Number{% endif %}
                                <span class="text-danger">*</span>
                            </label>
                            <input type="tel" class="form-control" id="phone" required>
                        </div>
                        <div class="col-md-6">
                            <label for="email" class="form-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}البريد الإلكتروني{% else %}Email{% endif %}
                            </label>
                            <input type="email" class="form-control" id="email">
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="addressAr" class="form-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}العنوان (عربي){% else %}Address (Arabic){% endif %}
                            </label>
                            <textarea class="form-control" id="addressAr" rows="2"></textarea>
                        </div>
                        <div class="col-md-6">
                            <label for="addressEn" class="form-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}العنوان (إنجليزي){% else %}Address (English){% endif %}
                            </label>
                            <textarea class="form-control" id="addressEn" rows="2"></textarea>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="nationalId" class="form-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}الرقم القومي{% else %}National ID{% endif %}
                            </label>
                            <input type="text" class="form-control" id="nationalId">
                        </div>
                        <div class="col-md-6">
                            <label for="birthDate" class="form-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}تاريخ الميلاد{% else %}Birth Date{% endif %}
                            </label>
                            <input type="date" class="form-control" id="birthDate">
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}الجنس{% else %}Gender{% endif %}
                            </label>
                            <div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="gender" id="genderMale" value="male" checked>
                                    <label class="form-check-label" for="genderMale">
                                        {% if CURRENT_LANGUAGE == 'ar' %}ذكر{% else %}Male{% endif %}
                                    </label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="gender" id="genderFemale" value="female">
                                    <label class="form-check-label" for="genderFemale">
                                        {% if CURRENT_LANGUAGE == 'ar' %}أنثى{% else %}Female{% endif %}
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch mt-4">
                                <input class="form-check-input" type="checkbox" id="isActive" checked>
                                <label class="form-check-label" for="isActive">
                                    {% if CURRENT_LANGUAGE == 'ar' %}نشط{% else %}Active{% endif %}
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">
                            {% if CURRENT_LANGUAGE == 'ar' %}ملاحظات{% else %}Notes{% endif %}
                        </label>
                        <textarea class="form-control" id="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {% if CURRENT_LANGUAGE == 'ar' %}إلغاء{% else %}Cancel{% endif %}
                </button>
                <button type="button" class="btn btn-primary" id="saveCustomerBtn">
                    {% if CURRENT_LANGUAGE == 'ar' %}حفظ{% else %}Save{% endif %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة عرض تفاصيل العميل -->
<div class="modal fade" id="customerDetailsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    {% if CURRENT_LANGUAGE == 'ar' %}تفاصيل العميل{% else %}Customer Details{% endif %}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <h6 class="fw-bold">{% if CURRENT_LANGUAGE == 'ar' %}الاسم:{% else %}Name:{% endif %}</h6>
                            <p id="detailName">أحمد محمد</p>
                        </div>
                        <div class="mb-3">
                            <h6 class="fw-bold">{% if CURRENT_LANGUAGE == 'ar' %}رقم الهاتف:{% else %}Phone:{% endif %}</h6>
                            <p id="detailPhone">01234567890</p>
                        </div>
                        <div class="mb-3">
                            <h6 class="fw-bold">{% if CURRENT_LANGUAGE == 'ar' %}البريد الإلكتروني:{% else %}Email:{% endif %}</h6>
                            <p id="detailEmail"><EMAIL></p>
                        </div>
                        <div class="mb-3">
                            <h6 class="fw-bold">{% if CURRENT_LANGUAGE == 'ar' %}العنوان:{% else %}Address:{% endif %}</h6>
                            <p id="detailAddress">القاهرة، مصر</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <h6 class="fw-bold">{% if CURRENT_LANGUAGE == 'ar' %}الرقم القومي:{% else %}National ID:{% endif %}</h6>
                            <p id="detailNationalId">29012345678901</p>
                        </div>
                        <div class="mb-3">
                            <h6 class="fw-bold">{% if CURRENT_LANGUAGE == 'ar' %}تاريخ الميلاد:{% else %}Birth Date:{% endif %}</h6>
                            <p id="detailBirthDate">1990-01-01</p>
                        </div>
                        <div class="mb-3">
                            <h6 class="fw-bold">{% if CURRENT_LANGUAGE == 'ar' %}الجنس:{% else %}Gender:{% endif %}</h6>
                            <p id="detailGender">ذكر</p>
                        </div>
                        <div class="mb-3">
                            <h6 class="fw-bold">{% if CURRENT_LANGUAGE == 'ar' %}إجمالي المشتريات:{% else %}Total Purchases:{% endif %}</h6>
                            <p id="detailTotalPurchases">1,500.00 ج.م</p>
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <h6 class="fw-bold">{% if CURRENT_LANGUAGE == 'ar' %}ملاحظات:{% else %}Notes:{% endif %}</h6>
                    <p id="detailNotes">لا توجد ملاحظات</p>
                </div>
                
                <div class="mt-4">
                    <h5>{% if CURRENT_LANGUAGE == 'ar' %}آخر المشتريات{% else %}Recent Purchases{% endif %}</h5>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>{% if CURRENT_LANGUAGE == 'ar' %}رقم الفاتورة{% else %}Invoice No.{% endif %}</th>
                                    <th>{% if CURRENT_LANGUAGE == 'ar' %}التاريخ{% else %}Date{% endif %}</th>
                                    <th>{% if CURRENT_LANGUAGE == 'ar' %}المبلغ{% else %}Amount{% endif %}</th>
                                    <th>{% if CURRENT_LANGUAGE == 'ar' %}الحالة{% else %}Status{% endif %}</th>
                                </tr>
                            </thead>
                            <tbody id="customerPurchases">
                                <tr>
                                    <td>INV-20240501-0001</td>
                                    <td>2024-05-01</td>
                                    <td>500.00 ج.م</td>
                                    <td><span class="badge bg-success">مدفوع</span></td>
                                </tr>
                                <tr>
                                    <td>INV-20240415-0023</td>
                                    <td>2024-04-15</td>
                                    <td>1,000.00 ج.م</td>
                                    <td><span class="badge bg-success">مدفوع</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {% if CURRENT_LANGUAGE == 'ar' %}إغلاق{% else %}Close{% endif %}
                </button>
                <button type="button" class="btn btn-primary" id="editFromDetailsBtn">
                    {% if CURRENT_LANGUAGE == 'ar' %}تعديل{% else %}Edit{% endif %}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تحميل الإحصائيات
        loadStats();
        
        // تهيئة الأحداث
        initializeEvents();
    });
    
    // تحميل الإحصائيات
    function loadStats() {
        // في الإصدار النهائي، سنقوم بتحميل الإحصائيات من الخادم
        document.getElementById('total-customers').textContent = '85';
        document.getElementById('active-customers').textContent = '78';
        document.getElementById('new-customers').textContent = '12';
        document.getElementById('total-sales').textContent = '45,000';
    }
    
    // تهيئة الأحداث
    function initializeEvents() {
        // زر البحث
        document.getElementById('searchBtn').addEventListener('click', function() {
            const searchTerm = document.getElementById('searchInput').value;
            const statusFilter = document.getElementById('statusFilter').value;
            
            // في الإصدار النهائي، سنقوم بالبحث من الخادم
            console.log('Search:', searchTerm, statusFilter);
        });
        
        // زر التحديث
        document.getElementById('refreshBtn').addEventListener('click', function() {
            loadStats();
        });
        
        // زر حفظ العميل
        document.getElementById('saveCustomerBtn').addEventListener('click', function() {
            const form = document.getElementById('customerForm');
            
            if (form.checkValidity()) {
                // جمع بيانات العميل
                const customerData = {
                    id: document.getElementById('customerId').value,
                    name_ar: document.getElementById('nameAr').value,
                    name_en: document.getElementById('nameEn').value,
                    phone: document.getElementById('phone').value,
                    email: document.getElementById('email').value,
                    address_ar: document.getElementById('addressAr').value,
                    address_en: document.getElementById('addressEn').value,
                    national_id: document.getElementById('nationalId').value,
                    birth_date: document.getElementById('birthDate').value,
                    gender: document.querySelector('input[name="gender"]:checked').value,
                    notes: document.getElementById('notes').value,
                    is_active: document.getElementById('isActive').checked
                };
                
                // في الإصدار النهائي، سنقوم بإرسال البيانات للخادم
                console.log('Save customer:', customerData);
                
                // إغلاق النافذة
                const modal = bootstrap.Modal.getInstance(document.getElementById('customerModal'));
                modal.hide();
                
                // إعادة تحميل الإحصائيات
                loadStats();
            } else {
                form.classList.add('was-validated');
            }
        });
        
        // أزرار عرض العميل
        document.querySelectorAll('.view-customer').forEach(button => {
            button.addEventListener('click', function() {
                const customerId = this.getAttribute('data-id');
                
                // في الإصدار النهائي، سنقوم بجلب بيانات العميل من الخادم
                
                // فتح النافذة
                const modal = new bootstrap.Modal(document.getElementById('customerDetailsModal'));
                modal.show();
            });
        });
        
        // أزرار تعديل العميل
        document.querySelectorAll('.edit-customer').forEach(button => {
            button.addEventListener('click', function() {
                const customerId = this.getAttribute('data-id');
                
                // في الإصدار النهائي، سنقوم بجلب بيانات العميل من الخادم
                document.getElementById('customerModalTitle').textContent = 'تعديل بيانات العميل';
                document.getElementById('customerId').value = customerId;
                
                // فتح النافذة
                const modal = new bootstrap.Modal(document.getElementById('customerModal'));
                modal.show();
            });
        });
        
        // أزرار حذف العميل
        document.querySelectorAll('.delete-customer').forEach(button => {
            button.addEventListener('click', function() {
                const customerId = this.getAttribute('data-id');
                
                if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
                    // في الإصدار النهائي، سنقوم بإرسال طلب الحذف للخادم
                    console.log('Delete customer:', customerId);
                    
                    // إعادة تحميل الإحصائيات
                    loadStats();
                }
            });
        });
        
        // زر التعديل من نافذة التفاصيل
        document.getElementById('editFromDetailsBtn').addEventListener('click', function() {
            // إغلاق نافذة التفاصيل
            const detailsModal = bootstrap.Modal.getInstance(document.getElementById('customerDetailsModal'));
            detailsModal.hide();
            
            // فتح نافذة التعديل
            document.getElementById('customerModalTitle').textContent = 'تعديل بيانات العميل';
            const editModal = new bootstrap.Modal(document.getElementById('customerModal'));
            editModal.show();
        });
    }
</script>
{% endblock %}
