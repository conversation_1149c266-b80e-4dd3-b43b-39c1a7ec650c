#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مسارات إدارة العملاء
Customer Management Routes
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from models import Customer
from app import db

bp = Blueprint('customers', __name__, url_prefix='/customers')

@bp.route('/')
@login_required
def index():
    """صفحة إدارة العملاء الرئيسية"""
    return render_template('customers/index.html')
