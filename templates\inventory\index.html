{% extends "base.html" %}

{% block title %}
    {% if CURRENT_LANGUAGE == 'ar' %}إدارة المخزون - {{ APP_NAME }}{% else %}Inventory Management - {{ APP_NAME_EN }}{% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            {% if CURRENT_LANGUAGE == 'ar' %}إدارة المخزون{% else %}Inventory Management{% endif %}
        </h1>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-primary" id="refreshBtn">
                <i class="fas fa-sync-alt"></i>
                {% if CURRENT_LANGUAGE == 'ar' %}تحديث{% else %}Refresh{% endif %}
            </button>
            <button class="btn btn-primary" id="addProductBtn" data-bs-toggle="modal" data-bs-target="#productModal">
                <i class="fas fa-plus"></i>
                {% if CURRENT_LANGUAGE == 'ar' %}إضافة منتج{% else %}Add Product{% endif %}
            </button>
        </div>
    </div>

    <!-- بطاقات الإحصائيات -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-value" id="total-products">0</div>
                            <div class="stat-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}إجمالي المنتجات{% else %}Total Products{% endif %}
                            </div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-boxes"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card warning">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-value" id="low-stock">0</div>
                            <div class="stat-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}منتجات منخفضة المخزون{% else %}Low Stock Products{% endif %}
                            </div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card danger">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-value" id="expired-products">0</div>
                            <div class="stat-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}منتجات منتهية الصلاحية{% else %}Expired Products{% endif %}
                            </div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-calendar-times"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card success">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-value" id="inventory-value">0</div>
                            <div class="stat-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}قيمة المخزون (ج.م){% else %}Inventory Value (EGP){% endif %}
                            </div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- البحث والفلترة -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" id="searchInput" placeholder="{% if CURRENT_LANGUAGE == 'ar' %}بحث...{% else %}Search...{% endif %}">
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="categoryFilter">
                        <option value="all">{% if CURRENT_LANGUAGE == 'ar' %}جميع الفئات{% else %}All Categories{% endif %}</option>
                        <!-- سيتم إضافة الفئات ديناميكياً -->
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="stockFilter">
                        <option value="all">{% if CURRENT_LANGUAGE == 'ar' %}جميع المنتجات{% else %}All Products{% endif %}</option>
                        <option value="low">{% if CURRENT_LANGUAGE == 'ar' %}منخفضة المخزون{% else %}Low Stock{% endif %}</option>
                        <option value="expired">{% if CURRENT_LANGUAGE == 'ar' %}منتهية الصلاحية{% else %}Expired{% endif %}</option>
                        <option value="active">{% if CURRENT_LANGUAGE == 'ar' %}نشطة{% else %}Active{% endif %}</option>
                        <option value="inactive">{% if CURRENT_LANGUAGE == 'ar' %}غير نشطة{% else %}Inactive{% endif %}</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button class="btn btn-primary w-100" id="searchBtn">
                        {% if CURRENT_LANGUAGE == 'ar' %}بحث{% else %}Search{% endif %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول المنتجات -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="productsTable">
                    <thead>
                        <tr>
                            <th>{% if CURRENT_LANGUAGE == 'ar' %}الرقم{% else %}ID{% endif %}</th>
                            <th>{% if CURRENT_LANGUAGE == 'ar' %}اسم المنتج{% else %}Product Name{% endif %}</th>
                            <th>{% if CURRENT_LANGUAGE == 'ar' %}الفئة{% else %}Category{% endif %}</th>
                            <th>{% if CURRENT_LANGUAGE == 'ar' %}الباركود{% else %}Barcode{% endif %}</th>
                            <th>{% if CURRENT_LANGUAGE == 'ar' %}سعر البيع{% else %}Selling Price{% endif %}</th>
                            <th>{% if CURRENT_LANGUAGE == 'ar' %}سعر التكلفة{% else %}Cost Price{% endif %}</th>
                            <th>{% if CURRENT_LANGUAGE == 'ar' %}المخزون{% else %}Stock{% endif %}</th>
                            <th>{% if CURRENT_LANGUAGE == 'ar' %}تاريخ الانتهاء{% else %}Expiry Date{% endif %}</th>
                            <th>{% if CURRENT_LANGUAGE == 'ar' %}الحالة{% else %}Status{% endif %}</th>
                            <th>{% if CURRENT_LANGUAGE == 'ar' %}الإجراءات{% else %}Actions{% endif %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- سيتم إضافة المنتجات ديناميكياً -->
                        <tr>
                            <td>1</td>
                            <td>باراسيتامول</td>
                            <td>أدوية</td>
                            <td>123456789</td>
                            <td>15.00 ج.م</td>
                            <td>10.00 ج.م</td>
                            <td><span class="badge bg-success">50</span></td>
                            <td>2025-12-31</td>
                            <td><span class="badge bg-success">نشط</span></td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-primary edit-product" data-id="1">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-danger delete-product" data-id="1">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- ترقيم الصفحات -->
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1">
                            {% if CURRENT_LANGUAGE == 'ar' %}السابق{% else %}Previous{% endif %}
                        </a>
                    </li>
                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                    <li class="page-item">
                        <a class="page-link" href="#">
                            {% if CURRENT_LANGUAGE == 'ar' %}التالي{% else %}Next{% endif %}
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- نافذة إضافة/تعديل منتج -->
<div class="modal fade" id="productModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="productModalTitle">
                    {% if CURRENT_LANGUAGE == 'ar' %}إضافة منتج جديد{% else %}Add New Product{% endif %}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="productForm" class="needs-validation" novalidate>
                    <input type="hidden" id="productId" value="">
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="nameAr" class="form-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}اسم المنتج (عربي){% else %}Product Name (Arabic){% endif %}
                                <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="nameAr" required>
                        </div>
                        <div class="col-md-6">
                            <label for="nameEn" class="form-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}اسم المنتج (إنجليزي){% else %}Product Name (English){% endif %}
                            </label>
                            <input type="text" class="form-control" id="nameEn">
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="category" class="form-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}الفئة{% else %}Category{% endif %}
                                <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="category" required>
                                <option value="">
                                    {% if CURRENT_LANGUAGE == 'ar' %}اختر الفئة{% else %}Select Category{% endif %}
                                </option>
                                <!-- سيتم إضافة الفئات ديناميكياً -->
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="barcode" class="form-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}الباركود{% else %}Barcode{% endif %}
                            </label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="barcode">
                                <button class="btn btn-outline-secondary" type="button" id="generateBarcodeBtn">
                                    <i class="fas fa-barcode"></i>
                                    {% if CURRENT_LANGUAGE == 'ar' %}توليد{% else %}Generate{% endif %}
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="sellingPrice" class="form-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}سعر البيع (ج.م){% else %}Selling Price (EGP){% endif %}
                                <span class="text-danger">*</span>
                            </label>
                            <input type="number" class="form-control" id="sellingPrice" step="0.01" min="0" required>
                        </div>
                        <div class="col-md-6">
                            <label for="costPrice" class="form-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}سعر التكلفة (ج.م){% else %}Cost Price (EGP){% endif %}
                                <span class="text-danger">*</span>
                            </label>
                            <input type="number" class="form-control" id="costPrice" step="0.01" min="0" required>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="stockQuantity" class="form-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}الكمية الحالية{% else %}Current Stock{% endif %}
                                <span class="text-danger">*</span>
                            </label>
                            <input type="number" class="form-control" id="stockQuantity" min="0" required>
                        </div>
                        <div class="col-md-4">
                            <label for="minStockLevel" class="form-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}الحد الأدنى للمخزون{% else %}Min Stock Level{% endif %}
                            </label>
                            <input type="number" class="form-control" id="minStockLevel" min="0" value="10">
                        </div>
                        <div class="col-md-4">
                            <label for="expiryDate" class="form-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}تاريخ الانتهاء{% else %}Expiry Date{% endif %}
                            </label>
                            <input type="date" class="form-control" id="expiryDate">
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="manufacturerAr" class="form-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}الشركة المصنعة (عربي){% else %}Manufacturer (Arabic){% endif %}
                            </label>
                            <input type="text" class="form-control" id="manufacturerAr">
                        </div>
                        <div class="col-md-6">
                            <label for="manufacturerEn" class="form-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}الشركة المصنعة (إنجليزي){% else %}Manufacturer (English){% endif %}
                            </label>
                            <input type="text" class="form-control" id="manufacturerEn">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">
                            {% if CURRENT_LANGUAGE == 'ar' %}الوصف{% else %}Description{% endif %}
                        </label>
                        <textarea class="form-control" id="description" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="isActive" checked>
                            <label class="form-check-label" for="isActive">
                                {% if CURRENT_LANGUAGE == 'ar' %}نشط{% else %}Active{% endif %}
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {% if CURRENT_LANGUAGE == 'ar' %}إلغاء{% else %}Cancel{% endif %}
                </button>
                <button type="button" class="btn btn-primary" id="saveProductBtn">
                    {% if CURRENT_LANGUAGE == 'ar' %}حفظ{% else %}Save{% endif %}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تحميل الفئات
        loadCategories();
        
        // تحميل المنتجات
        loadProducts();
        
        // تحميل الإحصائيات
        loadStats();
        
        // تهيئة الأحداث
        initializeEvents();
    });
    
    // تحميل الفئات
    function loadCategories() {
        // في الإصدار النهائي، سنقوم بتحميل الفئات من الخادم
        const categories = [
            { id: 1, name_ar: 'أدوية', name_en: 'Medicines' },
            { id: 2, name_ar: 'مستحضرات تجميل', name_en: 'Cosmetics' },
            { id: 3, name_ar: 'مكملات غذائية', name_en: 'Supplements' },
            { id: 4, name_ar: 'أدوات طبية', name_en: 'Medical Devices' }
        ];
        
        // إضافة الفئات إلى قائمة الفلتر
        const categoryFilter = document.getElementById('categoryFilter');
        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name_ar;
            categoryFilter.appendChild(option);
        });
        
        // إضافة الفئات إلى قائمة الإضافة/التعديل
        const categorySelect = document.getElementById('category');
        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name_ar;
            categorySelect.appendChild(option);
        });
    }
    
    // تحميل المنتجات
    function loadProducts() {
        // في الإصدار النهائي، سنقوم بتحميل المنتجات من الخادم
    }
    
    // تحميل الإحصائيات
    function loadStats() {
        // في الإصدار النهائي، سنقوم بتحميل الإحصائيات من الخادم
        document.getElementById('total-products').textContent = '150';
        document.getElementById('low-stock').textContent = '12';
        document.getElementById('expired-products').textContent = '5';
        document.getElementById('inventory-value').textContent = '25,000';
    }
    
    // تهيئة الأحداث
    function initializeEvents() {
        // زر البحث
        document.getElementById('searchBtn').addEventListener('click', function() {
            const searchTerm = document.getElementById('searchInput').value;
            const categoryId = document.getElementById('categoryFilter').value;
            const stockFilter = document.getElementById('stockFilter').value;
            
            // في الإصدار النهائي، سنقوم بالبحث من الخادم
            console.log('Search:', searchTerm, categoryId, stockFilter);
        });
        
        // زر التحديث
        document.getElementById('refreshBtn').addEventListener('click', function() {
            loadProducts();
            loadStats();
        });
        
        // زر توليد الباركود
        document.getElementById('generateBarcodeBtn').addEventListener('click', function() {
            const barcode = Math.floor(Math.random() * 9000000000000) + 1000000000000;
            document.getElementById('barcode').value = barcode;
        });
        
        // زر حفظ المنتج
        document.getElementById('saveProductBtn').addEventListener('click', function() {
            const form = document.getElementById('productForm');
            
            if (form.checkValidity()) {
                // جمع بيانات المنتج
                const productData = {
                    id: document.getElementById('productId').value,
                    name_ar: document.getElementById('nameAr').value,
                    name_en: document.getElementById('nameEn').value,
                    category_id: document.getElementById('category').value,
                    barcode: document.getElementById('barcode').value,
                    selling_price: document.getElementById('sellingPrice').value,
                    cost_price: document.getElementById('costPrice').value,
                    stock_quantity: document.getElementById('stockQuantity').value,
                    min_stock_level: document.getElementById('minStockLevel').value,
                    expiry_date: document.getElementById('expiryDate').value,
                    manufacturer_ar: document.getElementById('manufacturerAr').value,
                    manufacturer_en: document.getElementById('manufacturerEn').value,
                    description: document.getElementById('description').value,
                    is_active: document.getElementById('isActive').checked
                };
                
                // في الإصدار النهائي، سنقوم بإرسال البيانات للخادم
                console.log('Save product:', productData);
                
                // إغلاق النافذة
                const modal = bootstrap.Modal.getInstance(document.getElementById('productModal'));
                modal.hide();
                
                // إعادة تحميل المنتجات
                loadProducts();
                loadStats();
            } else {
                form.classList.add('was-validated');
            }
        });
        
        // أزرار تعديل المنتج
        document.querySelectorAll('.edit-product').forEach(button => {
            button.addEventListener('click', function() {
                const productId = this.getAttribute('data-id');
                
                // في الإصدار النهائي، سنقوم بجلب بيانات المنتج من الخادم
                document.getElementById('productModalTitle').textContent = 'تعديل المنتج';
                document.getElementById('productId').value = productId;
                
                // فتح النافذة
                const modal = new bootstrap.Modal(document.getElementById('productModal'));
                modal.show();
            });
        });
        
        // أزرار حذف المنتج
        document.querySelectorAll('.delete-product').forEach(button => {
            button.addEventListener('click', function() {
                const productId = this.getAttribute('data-id');
                
                if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
                    // في الإصدار النهائي، سنقوم بإرسال طلب الحذف للخادم
                    console.log('Delete product:', productId);
                    
                    // إعادة تحميل المنتجات
                    loadProducts();
                    loadStats();
                }
            });
        });
    }
</script>
{% endblock %}
