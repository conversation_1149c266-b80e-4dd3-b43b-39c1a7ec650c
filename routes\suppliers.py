#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مسارات إدارة الموردين
Supplier Management Routes
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from models import Supplier
from app import db

bp = Blueprint('suppliers', __name__, url_prefix='/suppliers')

@bp.route('/')
@login_required
def index():
    """صفحة إدارة الموردين الرئيسية"""
    return render_template('suppliers/index.html')
