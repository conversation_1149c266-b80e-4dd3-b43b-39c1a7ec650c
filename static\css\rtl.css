/* دعم اللغة العربية - التخطيط من اليمين لليسار */
/* Arabic Language Support - Right-to-Left Layout */

/* ========== الإعدادات العامة للـ RTL ========== */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] body {
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* ========== الشريط الجانبي ========== */
[dir="rtl"] .sidebar {
  right: 0;
  left: auto;
  border-right: none;
  border-left: 1px solid var(--gray-300);
}

[dir="rtl"] .main-content {
  margin-right: var(--sidebar-width);
  margin-left: 0;
}

[dir="rtl"] .sidebar .nav-link i {
  margin-right: 0;
  margin-left: 0.75rem;
}

[dir="rtl"] .sidebar .nav-link:hover {
  transform: translateX(-5px);
}

/* ========== شريط التنقل ========== */
[dir="rtl"] .navbar-nav {
  flex-direction: row-reverse;
}

[dir="rtl"] .dropdown-menu {
  right: 0;
  left: auto;
}

/* ========== النماذج والحقول ========== */
[dir="rtl"] .form-label {
  text-align: right;
}

[dir="rtl"] .input-group .btn {
  border-radius: var(--border-radius) 0 0 var(--border-radius);
}

[dir="rtl"] .input-group .form-control {
  border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

/* ========== الجداول ========== */
[dir="rtl"] .table {
  text-align: right;
}

[dir="rtl"] .table th,
[dir="rtl"] .table td {
  text-align: right;
}

/* ========== الأزرار ========== */
[dir="rtl"] .btn i {
  margin-right: 0;
  margin-left: 0.5rem;
}

[dir="rtl"] .btn-group .btn:first-child {
  border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

[dir="rtl"] .btn-group .btn:last-child {
  border-radius: var(--border-radius) 0 0 var(--border-radius);
}

/* ========== القوائم المنسدلة ========== */
[dir="rtl"] .dropdown-toggle::after {
  margin-right: 0.255em;
  margin-left: 0;
}

[dir="rtl"] .dropdown-menu {
  text-align: right;
}

/* ========== التنبيهات ========== */
[dir="rtl"] .alert {
  border-left: none;
  border-right: 4px solid;
}

[dir="rtl"] .alert-success {
  border-right-color: var(--success);
}

[dir="rtl"] .alert-danger {
  border-right-color: var(--danger);
}

[dir="rtl"] .alert-warning {
  border-right-color: var(--warning);
}

[dir="rtl"] .alert-info {
  border-right-color: var(--info);
}

/* ========== بطاقات الإحصائيات ========== */
[dir="rtl"] .stat-card {
  border-left: none;
  border-right: 4px solid var(--primary-blue);
}

[dir="rtl"] .stat-card.danger {
  border-right-color: var(--danger);
}

[dir="rtl"] .stat-card.success {
  border-right-color: var(--success);
}

[dir="rtl"] .stat-card.warning {
  border-right-color: var(--warning);
}

/* ========== التنقل بين الصفحات ========== */
[dir="rtl"] .pagination {
  flex-direction: row-reverse;
}

[dir="rtl"] .page-link {
  margin-right: -1px;
  margin-left: 0;
}

[dir="rtl"] .page-item:first-child .page-link {
  border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

[dir="rtl"] .page-item:last-child .page-link {
  border-radius: var(--border-radius) 0 0 var(--border-radius);
}

/* ========== البحث والفلاتر ========== */
[dir="rtl"] .search-box {
  text-align: right;
}

[dir="rtl"] .search-box .form-control {
  padding-right: 2.5rem;
  padding-left: 0.75rem;
}

[dir="rtl"] .search-box .search-icon {
  right: 0.75rem;
  left: auto;
}

/* ========== القوائم ========== */
[dir="rtl"] .list-group-item {
  text-align: right;
}

[dir="rtl"] .list-group-item .badge {
  float: left;
}

/* ========== الخطوات (Steps) ========== */
[dir="rtl"] .steps {
  flex-direction: row-reverse;
}

[dir="rtl"] .step::after {
  right: -50%;
  left: auto;
  transform: translateX(50%);
}

/* ========== الأيقونات ========== */
[dir="rtl"] .icon-left {
  margin-right: 0;
  margin-left: 0.5rem;
}

[dir="rtl"] .icon-right {
  margin-left: 0;
  margin-right: 0.5rem;
}

/* ========== التصميم المتجاوب للـ RTL ========== */
@media (max-width: 992px) {
  [dir="rtl"] .sidebar {
    transform: translateX(100%);
  }
  
  [dir="rtl"] .sidebar.show {
    transform: translateX(0);
  }
  
  [dir="rtl"] .main-content {
    margin-right: 0;
  }
}

/* ========== تحسينات الخط العربي ========== */
[dir="rtl"] h1, [dir="rtl"] h2, [dir="rtl"] h3, 
[dir="rtl"] h4, [dir="rtl"] h5, [dir="rtl"] h6 {
  font-weight: 600;
  line-height: 1.4;
}

[dir="rtl"] p {
  line-height: 1.8;
}

[dir="rtl"] .lead {
  font-size: 1.1rem;
  line-height: 1.7;
}

/* ========== تحسينات للأرقام العربية ========== */
[dir="rtl"] .arabic-numbers {
  font-family: 'Cairo', sans-serif;
  direction: ltr;
  unicode-bidi: embed;
}

/* ========== تحسينات للتواريخ ========== */
[dir="rtl"] .date-display {
  direction: ltr;
  text-align: left;
  display: inline-block;
}

/* ========== تحسينات للعملة ========== */
[dir="rtl"] .currency {
  direction: ltr;
  text-align: left;
  display: inline-block;
}

/* ========== تحسينات للجداول الكبيرة ========== */
[dir="rtl"] .table-responsive {
  direction: ltr;
}

[dir="rtl"] .table-responsive .table {
  direction: rtl;
}

/* ========== تحسينات للنماذج المعقدة ========== */
[dir="rtl"] .form-row {
  flex-direction: row-reverse;
}

[dir="rtl"] .form-group {
  text-align: right;
}

[dir="rtl"] .form-check {
  text-align: right;
  padding-right: 1.25rem;
  padding-left: 0;
}

[dir="rtl"] .form-check-input {
  margin-right: -1.25rem;
  margin-left: 0;
}

/* ========== تحسينات للقوائم المنسدلة المتقدمة ========== */
[dir="rtl"] .select2-container--default .select2-selection--single {
  text-align: right;
}

[dir="rtl"] .select2-container--default .select2-selection--single .select2-selection__arrow {
  left: 1px;
  right: auto;
}

/* ========== تحسينات للتقويم ========== */
[dir="rtl"] .calendar {
  direction: rtl;
}

[dir="rtl"] .calendar .calendar-header {
  flex-direction: row-reverse;
}

/* ========== تحسينات للرسوم البيانية ========== */
[dir="rtl"] .chart-container {
  direction: ltr;
}

[dir="rtl"] .chart-legend {
  direction: rtl;
  text-align: right;
}

/* ========== تحسينات للطباعة ========== */
@media print {
  [dir="rtl"] body {
    direction: rtl;
    text-align: right;
  }
  
  [dir="rtl"] .no-print {
    display: none !important;
  }
}

/* ========== تحسينات للوضع المظلم مع RTL ========== */
[dir="rtl"].dark-theme .sidebar {
  border-left-color: var(--gray-600);
}

[dir="rtl"].dark-theme .alert {
  border-right-width: 4px;
  border-left-width: 0;
}

/* ========== تحسينات للتفاعل باللمس ========== */
@media (hover: none) and (pointer: coarse) {
  [dir="rtl"] .sidebar .nav-link {
    padding: 1rem 1.5rem;
  }
  
  [dir="rtl"] .btn {
    padding: 0.75rem 1.5rem;
  }
}
