// نظام نقطة البيع - JavaScript
// Point of Sale System - JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة نقطة البيع
    initializePOS();
});

// المتغيرات العامة
let cart = [];
let currentProduct = null;
let currentSaleId = null;
let currentCustomerId = null;
let categories = [];

// تهيئة نقطة البيع
function initializePOS() {
    console.log('تم تحميل نظام نقطة البيع');
    
    // تحميل الفئات
    loadCategories();
    
    // تحميل المنتجات
    loadProducts();
    
    // تهيئة البحث
    initializeSearch();
    
    // تهيئة الأحداث
    initializeEvents();
    
    // تحديث السلة
    updateCart();
}

// تحميل الفئات
function loadCategories() {
    fetch('/inventory/api/categories')
        .then(response => response.json())
        .then(data => {
            categories = data;
            
            // إضافة الفئات للفلتر
            const categoryFilter = document.getElementById('categoryFilter');
            data.forEach(category => {
                const button = document.createElement('button');
                button.className = 'btn btn-sm btn-outline-primary';
                button.setAttribute('data-category', category.id);
                button.textContent = category.name_ar;
                button.addEventListener('click', function() {
                    // إزالة الفئة النشطة
                    document.querySelectorAll('#categoryFilter .btn').forEach(btn => {
                        btn.classList.remove('active');
                    });
                    
                    // تعيين الفئة النشطة
                    this.classList.add('active');
                    
                    // تصفية المنتجات
                    filterProducts(category.id);
                });
                
                categoryFilter.appendChild(button);
            });
        })
        .catch(error => {
            console.error('Error loading categories:', error);
            showErrorMessage('حدث خطأ في تحميل الفئات');
        });
}

// تحميل المنتجات
function loadProducts() {
    // في الإصدار النهائي، سنقوم بتحميل المنتجات من الخادم
    // لكن الآن سنستخدم بيانات تجريبية
    
    const productsContainer = document.getElementById('productsContainer');
    productsContainer.innerHTML = '';
    
    // إضافة منتجات تجريبية
    for (let i = 1; i <= 20; i++) {
        const productCard = createProductCard({
            id: i,
            name_ar: `منتج ${i}`,
            name_en: `Product ${i}`,
            price: (Math.random() * 100 + 10).toFixed(2),
            stock_quantity: Math.floor(Math.random() * 100),
            category_id: Math.ceil(Math.random() * 4)
        });
        
        productsContainer.appendChild(productCard);
    }
}

// إنشاء بطاقة منتج
function createProductCard(product) {
    const col = document.createElement('div');
    col.className = 'col';
    col.setAttribute('data-category', product.category_id);
    
    const card = document.createElement('div');
    card.className = 'card pos-product-card';
    card.addEventListener('click', function() {
        showProductModal(product);
    });
    
    const imageDiv = document.createElement('div');
    imageDiv.className = 'pos-product-image';
    
    const icon = document.createElement('i');
    icon.className = 'fas fa-pills';
    imageDiv.appendChild(icon);
    
    const cardBody = document.createElement('div');
    cardBody.className = 'card-body';
    
    const title = document.createElement('h6');
    title.className = 'card-title';
    title.textContent = product.name_ar;
    
    const priceRow = document.createElement('div');
    priceRow.className = 'd-flex justify-content-between align-items-center';
    
    const price = document.createElement('span');
    price.className = 'text-primary fw-bold';
    price.textContent = `${parseFloat(product.price).toFixed(2)} ج.م`;
    
    const stock = document.createElement('span');
    stock.className = product.stock_quantity > 0 ? 'badge bg-success' : 'badge bg-danger';
    stock.textContent = product.stock_quantity > 0 ? 'متوفر' : 'غير متوفر';
    
    priceRow.appendChild(price);
    priceRow.appendChild(stock);
    
    cardBody.appendChild(title);
    cardBody.appendChild(priceRow);
    
    card.appendChild(imageDiv);
    card.appendChild(cardBody);
    
    col.appendChild(card);
    
    return col;
}

// تصفية المنتجات حسب الفئة
function filterProducts(categoryId) {
    const products = document.querySelectorAll('#productsContainer .col');
    
    products.forEach(product => {
        if (categoryId === 'all' || product.getAttribute('data-category') === categoryId.toString()) {
            product.style.display = '';
        } else {
            product.style.display = 'none';
        }
    });
}

// تهيئة البحث
function initializeSearch() {
    const productSearch = document.getElementById('productSearch');
    const searchResults = document.getElementById('searchResults');
    
    productSearch.addEventListener('input', function() {
        const query = this.value.trim();
        
        if (query.length < 2) {
            searchResults.style.display = 'none';
            return;
        }
        
        // في الإصدار النهائي، سنقوم بالبحث من الخادم
        // لكن الآن سنستخدم بيانات تجريبية
        
        searchResults.innerHTML = '';
        searchResults.style.display = 'block';
        
        // إضافة نتائج بحث تجريبية
        for (let i = 1; i <= 5; i++) {
            const item = document.createElement('div');
            item.className = 'pos-search-item';
            item.innerHTML = `
                <div class="d-flex justify-content-between">
                    <div>
                        <strong>${query} ${i}</strong>
                        <div class="small text-muted">باركود: 123456789${i}</div>
                    </div>
                    <div class="text-primary">${(Math.random() * 100 + 10).toFixed(2)} ج.م</div>
                </div>
            `;
            
            item.addEventListener('click', function() {
                showProductModal({
                    id: i,
                    name_ar: `${query} ${i}`,
                    name_en: `${query} ${i}`,
                    price: (Math.random() * 100 + 10).toFixed(2),
                    stock_quantity: Math.floor(Math.random() * 100),
                    category_id: Math.ceil(Math.random() * 4)
                });
                
                searchResults.style.display = 'none';
                productSearch.value = '';
            });
            
            searchResults.appendChild(item);
        }
    });
    
    // إخفاء نتائج البحث عند النقر خارجها
    document.addEventListener('click', function(event) {
        if (!productSearch.contains(event.target) && !searchResults.contains(event.target)) {
            searchResults.style.display = 'none';
        }
    });
    
    // بحث العملاء
    const customerSearch = document.getElementById('customerSearch');
    const customerResults = document.getElementById('customerResults');
    
    customerSearch.addEventListener('input', function() {
        const query = this.value.trim();
        
        if (query.length < 2) {
            customerResults.style.display = 'none';
            return;
        }
        
        // في الإصدار النهائي، سنقوم بالبحث من الخادم
        // لكن الآن سنستخدم بيانات تجريبية
        
        customerResults.innerHTML = '';
        customerResults.style.display = 'block';
        
        // إضافة نتائج بحث تجريبية
        for (let i = 1; i <= 3; i++) {
            const item = document.createElement('div');
            item.className = 'pos-search-item';
            item.innerHTML = `
                <div class="d-flex justify-content-between">
                    <div>
                        <strong>عميل ${query} ${i}</strong>
                        <div class="small text-muted">هاتف: 0123456789${i}</div>
                    </div>
                </div>
            `;
            
            item.addEventListener('click', function() {
                customerSearch.value = `عميل ${query} ${i}`;
                currentCustomerId = i;
                customerResults.style.display = 'none';
            });
            
            customerResults.appendChild(item);
        }
    });
    
    // إخفاء نتائج البحث عند النقر خارجها
    document.addEventListener('click', function(event) {
        if (!customerSearch.contains(event.target) && !customerResults.contains(event.target)) {
            customerResults.style.display = 'none';
        }
    });
}

// تهيئة الأحداث
function initializeEvents() {
    // زر الباركود
    document.getElementById('barcodeBtn').addEventListener('click', function() {
        const barcode = prompt('أدخل الباركود:');
        if (barcode) {
            // البحث عن المنتج بالباركود
            // في الإصدار النهائي، سنقوم بالبحث من الخادم
            showProductModal({
                id: 999,
                name_ar: `منتج بباركود ${barcode}`,
                name_en: `Product with barcode ${barcode}`,
                price: (Math.random() * 100 + 10).toFixed(2),
                stock_quantity: Math.floor(Math.random() * 100),
                category_id: Math.ceil(Math.random() * 4)
            });
        }
    });
    
    // زر إضافة عميل
    document.getElementById('addCustomerBtn').addEventListener('click', function() {
        alert('سيتم فتح نافذة إضافة عميل جديد');
    });
    
    // زر فاتورة جديدة
    document.getElementById('newInvoiceBtn').addEventListener('click', function() {
        if (cart.length > 0) {
            if (confirm('هل أنت متأكد من إنشاء فاتورة جديدة؟ سيتم مسح الفاتورة الحالية.')) {
                resetInvoice();
            }
        } else {
            resetInvoice();
        }
    });
    
    // زر تعليق الفاتورة
    document.getElementById('suspendInvoiceBtn').addEventListener('click', function() {
        if (cart.length === 0) {
            showErrorMessage('لا يمكن تعليق فاتورة فارغة');
            return;
        }
        
        suspendInvoice();
    });
    
    // زر عرض الفواتير المعلقة
    document.getElementById('showSuspendedBtn').addEventListener('click', function() {
        const modal = new bootstrap.Modal(document.getElementById('suspendedInvoicesModal'));
        modal.show();
    });
    
    // أزرار استكمال الفواتير المعلقة
    document.querySelectorAll('.resume-invoice').forEach(button => {
        button.addEventListener('click', function() {
            const saleId = this.getAttribute('data-id');
            resumeSuspendedInvoice(saleId);
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('suspendedInvoicesModal'));
            modal.hide();
        });
    });
    
    // أزرار حذف الفواتير المعلقة
    document.querySelectorAll('.delete-invoice').forEach(button => {
        button.addEventListener('click', function() {
            const saleId = this.getAttribute('data-id');
            if (confirm('هل أنت متأكد من حذف الفاتورة المعلقة؟')) {
                deleteSuspendedInvoice(saleId);
            }
        });
    });
    
    // زر إتمام البيع
    document.getElementById('checkoutBtn').addEventListener('click', function() {
        if (cart.length === 0) {
            showErrorMessage('لا يمكن إتمام فاتورة فارغة');
            return;
        }
        
        completeSale();
    });
    
    // زر إلغاء الفاتورة
    document.getElementById('cancelBtn').addEventListener('click', function() {
        if (cart.length > 0) {
            if (confirm('هل أنت متأكد من إلغاء الفاتورة؟')) {
                resetInvoice();
            }
        }
    });
    
    // تغيير نسبة الخصم
    document.getElementById('discountPercentage').addEventListener('input', function() {
        updateTotals();
    });
    
    // تهيئة نافذة المنتج
    initializeProductModal();
}

// تهيئة نافذة المنتج
function initializeProductModal() {
    const productQuantity = document.getElementById('productQuantity');
    const productPrice = document.getElementById('productPrice');
    const productDiscount = document.getElementById('productDiscount');
    
    // حساب الإجمالي عند تغيير الكمية أو السعر أو الخصم
    function calculateTotal() {
        const quantity = parseInt(productQuantity.value) || 0;
        const price = parseFloat(productPrice.value) || 0;
        const discount = parseFloat(productDiscount.value) || 0;
        
        const total = (quantity * price) - discount;
        document.getElementById('productTotal').value = total.toFixed(2);
    }
    
    productQuantity.addEventListener('input', calculateTotal);
    productPrice.addEventListener('input', calculateTotal);
    productDiscount.addEventListener('input', calculateTotal);
    
    // زر إضافة للسلة
    document.getElementById('addToCartBtn').addEventListener('click', function() {
        if (!currentProduct) return;
        
        const quantity = parseInt(productQuantity.value) || 0;
        const price = parseFloat(productPrice.value) || 0;
        const discount = parseFloat(productDiscount.value) || 0;
        const total = (quantity * price) - discount;
        
        if (quantity <= 0) {
            showErrorMessage('يجب أن تكون الكمية أكبر من صفر');
            return;
        }
        
        if (price <= 0) {
            showErrorMessage('يجب أن يكون السعر أكبر من صفر');
            return;
        }
        
        // التحقق من توفر الكمية
        if (quantity > currentProduct.stock_quantity) {
            showErrorMessage('الكمية المطلوبة غير متوفرة');
            return;
        }
        
        // إضافة المنتج للسلة
        const cartItem = {
            product_id: currentProduct.id,
            name: currentProduct.name_ar,
            quantity: quantity,
            unit_price: price,
            discount: discount,
            total: total
        };
        
        // التحقق من وجود المنتج في السلة
        const existingItemIndex = cart.findIndex(item => item.product_id === cartItem.product_id);
        
        if (existingItemIndex !== -1) {
            // تحديث الكمية والسعر والخصم والإجمالي
            cart[existingItemIndex] = cartItem;
        } else {
            // إضافة منتج جديد
            cart.push(cartItem);
        }
        
        // تحديث السلة
        updateCart();
        
        // إغلاق النافذة
        const modal = bootstrap.Modal.getInstance(document.getElementById('productModal'));
        modal.hide();
    });
}

// عرض نافذة المنتج
function showProductModal(product) {
    currentProduct = product;
    
    document.getElementById('productModalTitle').textContent = product.name_ar;
    document.getElementById('productQuantity').value = 1;
    document.getElementById('productPrice').value = product.price;
    document.getElementById('productDiscount').value = 0;
    document.getElementById('availableStock').textContent = product.stock_quantity;
    
    // حساب الإجمالي
    const quantity = parseInt(document.getElementById('productQuantity').value) || 0;
    const price = parseFloat(document.getElementById('productPrice').value) || 0;
    const discount = parseFloat(document.getElementById('productDiscount').value) || 0;
    
    const total = (quantity * price) - discount;
    document.getElementById('productTotal').value = total.toFixed(2);
    
    // عرض النافذة
    const modal = new bootstrap.Modal(document.getElementById('productModal'));
    modal.show();
}

// تحديث السلة
function updateCart() {
    const cartItems = document.getElementById('cartItems');
    
    if (cart.length === 0) {
        cartItems.innerHTML = `
            <div class="pos-cart-empty">
                <i class="fas fa-shopping-cart"></i>
                <h5>السلة فارغة</h5>
                <p>أضف منتجات للفاتورة</p>
            </div>
        `;
    } else {
        let html = '';
        
        cart.forEach((item, index) => {
            html += `
                <div class="pos-cart-item">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">${item.name}</h6>
                        <button class="btn btn-sm btn-outline-danger" onclick="removeFromCart(${index})">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="pos-cart-item-price">${item.unit_price.toFixed(2)} ج.م</div>
                        <div class="d-flex align-items-center">
                            <button class="btn btn-sm btn-outline-secondary" onclick="decreaseQuantity(${index})">-</button>
                            <input type="text" class="form-control form-control-sm pos-cart-item-quantity mx-1" value="${item.quantity}" readonly>
                            <button class="btn btn-sm btn-outline-secondary" onclick="increaseQuantity(${index})">+</button>
                        </div>
                        <div class="pos-cart-item-total">${item.total.toFixed(2)} ج.م</div>
                    </div>
                    ${item.discount > 0 ? `<div class="small text-danger">خصم: ${item.discount.toFixed(2)} ج.م</div>` : ''}
                </div>
            `;
        });
        
        cartItems.innerHTML = html;
    }
    
    // تحديث المجاميع
    updateTotals();
}

// تحديث المجاميع
function updateTotals() {
    // حساب المجموع الفرعي
    const subtotal = cart.reduce((total, item) => total + item.total, 0);
    
    // حساب الخصم
    const discountPercentage = parseFloat(document.getElementById('discountPercentage').value) || 0;
    const discountAmount = subtotal * (discountPercentage / 100);
    
    // حساب الضريبة
    const taxableAmount = subtotal - discountAmount;
    const taxAmount = taxableAmount * 0.14;  // 14% ضريبة القيمة المضافة
    
    // حساب الإجمالي
    const totalAmount = taxableAmount + taxAmount;
    
    // عرض المجاميع
    document.getElementById('subtotal').textContent = `${subtotal.toFixed(2)} ج.م`;
    document.getElementById('discountAmount').textContent = `${discountAmount.toFixed(2)} ج.م`;
    document.getElementById('taxAmount').textContent = `${taxAmount.toFixed(2)} ج.م`;
    document.getElementById('totalAmount').textContent = `${totalAmount.toFixed(2)} ج.م`;
}

// إزالة منتج من السلة
function removeFromCart(index) {
    cart.splice(index, 1);
    updateCart();
}

// زيادة الكمية
function increaseQuantity(index) {
    cart[index].quantity++;
    cart[index].total = (cart[index].quantity * cart[index].unit_price) - cart[index].discount;
    updateCart();
}

// إنقاص الكمية
function decreaseQuantity(index) {
    if (cart[index].quantity > 1) {
        cart[index].quantity--;
        cart[index].total = (cart[index].quantity * cart[index].unit_price) - cart[index].discount;
        updateCart();
    }
}

// إعادة تعيين الفاتورة
function resetInvoice() {
    cart = [];
    currentSaleId = null;
    currentCustomerId = null;
    document.getElementById('customerSearch').value = '';
    document.getElementById('invoiceNumber').textContent = 'جديد';
    document.getElementById('discountPercentage').value = 0;
    updateCart();
}

// تعليق الفاتورة
function suspendInvoice() {
    // في الإصدار النهائي، سنقوم بإرسال البيانات للخادم
    alert('تم تعليق الفاتورة بنجاح');
    resetInvoice();
}

// استكمال فاتورة معلقة
function resumeSuspendedInvoice(saleId) {
    // في الإصدار النهائي، سنقوم بجلب بيانات الفاتورة من الخادم
    alert(`سيتم استكمال الفاتورة رقم ${saleId}`);
}

// حذف فاتورة معلقة
function deleteSuspendedInvoice(saleId) {
    // في الإصدار النهائي، سنقوم بحذف الفاتورة من الخادم
    alert(`سيتم حذف الفاتورة رقم ${saleId}`);
    
    // إزالة الصف من الجدول
    const row = document.querySelector(`.delete-invoice[data-id="${saleId}"]`).closest('tr');
    row.remove();
}

// إتمام البيع
function completeSale() {
    // في الإصدار النهائي، سنقوم بإرسال البيانات للخادم
    alert('تم إتمام البيع بنجاح');
    
    // طباعة الفاتورة
    printInvoice();
    
    // إعادة تعيين الفاتورة
    resetInvoice();
}

// طباعة الفاتورة
function printInvoice() {
    // في الإصدار النهائي، سنقوم بفتح نافذة الطباعة
    alert('سيتم طباعة الفاتورة');
}

// عرض رسالة خطأ
function showErrorMessage(message) {
    alert(message);
}
