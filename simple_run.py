#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل بسيط لنظام إدارة الصيدليات
Simple Runner for Pharmacy Management System
"""

from flask import Flask, render_template, request, redirect, url_for, session, flash
import os
from datetime import datetime

app = Flask(__name__)
app.secret_key = 'pharmacy-management-secret-key-2024'

# قائمة المستخدمين (في الإصدار النهائي ستكون في قاعدة البيانات)
users = {
    'admin': {
        'password': 'admin123',
        'full_name_ar': 'المدير العام',
        'full_name_en': 'System Administrator',
        'role': 'admin'
    }
}

# المسارات
@app.route('/')
def index():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if 'user_id' in session:
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        if username in users and users[username]['password'] == password:
            session['user_id'] = username
            session['user_role'] = users[username]['role']
            session['user_name'] = users[username]['full_name_ar']
            flash('تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template('auth/login.html',
                          CURRENT_LANGUAGE='ar',
                          LANGUAGES={'ar': 'العربية', 'en': 'English'},
                          APP_NAME='نظام إدارة الصيدليات',
                          APP_NAME_EN='Pharmacy Management System')

@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
def dashboard():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    # بيانات تجريبية للوحة التحكم
    stats = {
        'total_products': 150,
        'low_stock_count': 12,
        'expired_count': 5,
        'today_sales': 5000.0,
        'month_sales': 45000.0,
        'year_sales': 350000.0,
        'total_customers': 85,
        'total_suppliers': 15,
        'suspended_invoices': 3,
        'inventory_value': 250000.0
    }

    # منتجات منخفضة المخزون
    low_stock_products = [
        {'name_ar': 'باراسيتامول', 'stock_quantity': 5, 'min_stock_level': 10},
        {'name_ar': 'أموكسيسيلين', 'stock_quantity': 3, 'min_stock_level': 10},
        {'name_ar': 'فيتامين سي', 'stock_quantity': 8, 'min_stock_level': 15}
    ]

    # منتجات منتهية الصلاحية
    expired_products = [
        {'name_ar': 'أسبرين', 'expiry_date': datetime(2024, 4, 30), 'stock_quantity': 12},
        {'name_ar': 'ديكلوفيناك', 'expiry_date': datetime(2024, 5, 15), 'stock_quantity': 8}
    ]

    # آخر المبيعات
    recent_sales = [
        {'invoice_number': 'INV-20240501-0001', 'customer': {'name_ar': 'أحمد محمد'}, 'total_amount': 500.0, 'sale_date': datetime(2024, 5, 1, 10, 30), 'user': {'full_name_ar': 'المدير العام'}},
        {'invoice_number': 'INV-20240501-0002', 'customer': {'name_ar': 'سارة أحمد'}, 'total_amount': 750.0, 'sale_date': datetime(2024, 5, 1, 14, 15), 'user': {'full_name_ar': 'المدير العام'}},
        {'invoice_number': 'INV-20240430-0005', 'customer': None, 'total_amount': 320.0, 'sale_date': datetime(2024, 4, 30, 16, 45), 'user': {'full_name_ar': 'المدير العام'}}
    ]

    return render_template('dashboard/index.html',
                          current_user={'is_authenticated': True, 'username': session['user_id'], 'full_name_ar': session['user_name'], 'role': session['user_role']},
                          CURRENT_LANGUAGE='ar',
                          LANGUAGES={'ar': 'العربية', 'en': 'English'},
                          APP_NAME='نظام إدارة الصيدليات',
                          APP_NAME_EN='Pharmacy Management System',
                          stats=stats,
                          low_stock_products=low_stock_products,
                          expired_products=expired_products,
                          recent_sales=recent_sales,
                          suspended_sales=3,
                          request={'endpoint': 'dashboard.index'})

@app.route('/inventory')
def inventory():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    return render_template('inventory/index.html',
                          current_user={'is_authenticated': True, 'username': session['user_id'], 'full_name_ar': session['user_name'], 'role': session['user_role']},
                          CURRENT_LANGUAGE='ar',
                          LANGUAGES={'ar': 'العربية', 'en': 'English'},
                          APP_NAME='نظام إدارة الصيدليات',
                          APP_NAME_EN='Pharmacy Management System',
                          request={'endpoint': 'inventory.index'})

@app.route('/customers')
def customers():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    return render_template('customers/index.html',
                          current_user={'is_authenticated': True, 'username': session['user_id'], 'full_name_ar': session['user_name'], 'role': session['user_role']},
                          CURRENT_LANGUAGE='ar',
                          LANGUAGES={'ar': 'العربية', 'en': 'English'},
                          APP_NAME='نظام إدارة الصيدليات',
                          APP_NAME_EN='Pharmacy Management System',
                          request={'endpoint': 'customers.index'})

@app.route('/pos')
def pos():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    # فواتير معلقة تجريبية
    suspended_sales = [
        {'id': 1, 'invoice_number': 'INV-20240501-0003', 'customer': {'name_ar': 'محمد علي'}, 'total_amount': 450.0, 'created_at': datetime(2024, 5, 1, 11, 30)},
        {'id': 2, 'invoice_number': 'INV-20240501-0004', 'customer': None, 'total_amount': 280.0, 'created_at': datetime(2024, 5, 1, 15, 45)}
    ]

    return render_template('pos/index.html',
                          current_user={'is_authenticated': True, 'username': session['user_id'], 'full_name_ar': session['user_name'], 'role': session['user_role']},
                          CURRENT_LANGUAGE='ar',
                          LANGUAGES={'ar': 'العربية', 'en': 'English'},
                          APP_NAME='نظام إدارة الصيدليات',
                          APP_NAME_EN='Pharmacy Management System',
                          suspended_sales=suspended_sales,
                          request={'endpoint': 'pos.index'})

@app.route('/reports')
def reports():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    return render_template('reports/index.html',
                          current_user={'is_authenticated': True, 'username': session['user_id'], 'full_name_ar': session['user_name'], 'role': session['user_role']},
                          CURRENT_LANGUAGE='ar',
                          LANGUAGES={'ar': 'العربية', 'en': 'English'},
                          APP_NAME='نظام إدارة الصيدليات',
                          APP_NAME_EN='Pharmacy Management System',
                          request={'endpoint': 'reports.index'})

@app.route('/set_language/<language>')
def set_language(language):
    session['language'] = language
    return redirect(request.referrer or url_for('index'))

@app.route('/sales')
def sales():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    return render_template('sales/index.html',
                          current_user={'is_authenticated': True, 'username': session['user_id'], 'full_name_ar': session['user_name'], 'role': session['user_role']},
                          CURRENT_LANGUAGE='ar',
                          LANGUAGES={'ar': 'العربية', 'en': 'English'},
                          APP_NAME='نظام إدارة الصيدليات',
                          APP_NAME_EN='Pharmacy Management System',
                          request={'endpoint': 'sales.index'})

@app.route('/purchases')
def purchases():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    return render_template('purchases/index.html',
                          current_user={'is_authenticated': True, 'username': session['user_id'], 'full_name_ar': session['user_name'], 'role': session['user_role']},
                          CURRENT_LANGUAGE='ar',
                          LANGUAGES={'ar': 'العربية', 'en': 'English'},
                          APP_NAME='نظام إدارة الصيدليات',
                          APP_NAME_EN='Pharmacy Management System',
                          request={'endpoint': 'purchases.index'})

@app.route('/suppliers')
def suppliers():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    return render_template('suppliers/index.html',
                          current_user={'is_authenticated': True, 'username': session['user_id'], 'full_name_ar': session['user_name'], 'role': session['user_role']},
                          CURRENT_LANGUAGE='ar',
                          LANGUAGES={'ar': 'العربية', 'en': 'English'},
                          APP_NAME='نظام إدارة الصيدليات',
                          APP_NAME_EN='Pharmacy Management System',
                          request={'endpoint': 'suppliers.index'})

@app.route('/settings')
def settings():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    return render_template('settings/index.html',
                          current_user={'is_authenticated': True, 'username': session['user_id'], 'full_name_ar': session['user_name'], 'role': session['user_role']},
                          CURRENT_LANGUAGE='ar',
                          LANGUAGES={'ar': 'العربية', 'en': 'English'},
                          APP_NAME='نظام إدارة الصيدليات',
                          APP_NAME_EN='Pharmacy Management System',
                          request={'endpoint': 'settings.index'})

if __name__ == '__main__':
    app.run(debug=True)
