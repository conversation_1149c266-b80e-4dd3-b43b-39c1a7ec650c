/* صفحة تسجيل الدخول - التصميم المخصص */
/* Login Page - Custom Styles */

.login-page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  font-family: var(--font-arabic);
}

[dir="ltr"] .login-page {
  font-family: var(--font-english);
}

.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.login-form-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  margin: 0 auto;
}

.login-logo {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-red) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.login-logo i {
  font-size: 2rem;
  color: white;
}

.login-title {
  color: var(--dark);
  font-weight: 700;
  margin-bottom: 0.5rem;
  font-size: 1.8rem;
}

.login-subtitle {
  color: var(--gray-600);
  margin-bottom: 2rem;
  font-size: 1rem;
}

.login-form .form-label {
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: 0.5rem;
}

.login-form .form-control {
  border: 2px solid var(--gray-300);
  border-radius: 12px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: all 0.3s ease;
  background-color: rgba(255, 255, 255, 0.9);
}

.login-form .form-control:focus {
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  background-color: white;
}

.login-form .btn-primary {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-red) 100%);
  border: none;
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.login-form .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, var(--secondary-blue) 0%, var(--secondary-red) 100%);
}

.form-check-input:checked {
  background-color: var(--primary-blue);
  border-color: var(--primary-blue);
}

.language-switcher .btn {
  margin: 0 0.25rem;
  border-radius: 20px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.language-switcher .btn.active {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-red) 100%);
  color: white;
  border-color: transparent;
}

/* الجانب الأيمن - الخلفية */
.login-bg {
  background: linear-gradient(135deg, 
    rgba(13, 110, 253, 0.9) 0%, 
    rgba(220, 53, 69, 0.9) 100%),
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="pharmacy-pattern" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/><path d="M45,45 L55,45 M50,40 L50,60" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23pharmacy-pattern)"/></svg>');
  background-size: cover;
  background-position: center;
  height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-bg-overlay {
  background: rgba(0, 0, 0, 0.3);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-bg-content {
  text-align: center;
  color: white;
  padding: 2rem;
  max-width: 500px;
}

.login-bg-content h3 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.login-bg-content p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  line-height: 1.6;
}

.features-list {
  text-align: right;
}

[dir="ltr"] .features-list {
  text-align: left;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  font-size: 1.1rem;
  opacity: 0.9;
}

.feature-item i {
  margin-left: 1rem;
  color: #28a745;
  font-size: 1.2rem;
}

[dir="ltr"] .feature-item i {
  margin-left: 0;
  margin-right: 1rem;
}

/* تأثيرات الحركة */
@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.login-form-container {
  animation: slideInFromLeft 0.8s ease-out;
}

.login-bg-content {
  animation: slideInFromRight 0.8s ease-out;
}

/* التصميم المتجاوب */
@media (max-width: 992px) {
  .login-bg {
    display: none;
  }
  
  .login-form-container {
    margin: 2rem auto;
    padding: 2rem;
  }
  
  .login-title {
    font-size: 1.5rem;
  }
  
  .login-bg-content h3 {
    font-size: 2rem;
  }
}

@media (max-width: 576px) {
  .login-form-container {
    margin: 1rem;
    padding: 1.5rem;
    border-radius: 15px;
  }
  
  .login-logo {
    width: 60px;
    height: 60px;
  }
  
  .login-logo i {
    font-size: 1.5rem;
  }
  
  .login-title {
    font-size: 1.3rem;
  }
  
  .login-subtitle {
    font-size: 0.9rem;
  }
}

/* تأثيرات إضافية */
.login-form .form-control:focus {
  animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

.btn-primary:active {
  transform: translateY(0) !important;
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
  .login-form-container {
    background: rgba(33, 37, 41, 0.95);
    color: white;
  }
  
  .login-title {
    color: white;
  }
  
  .login-subtitle {
    color: var(--gray-400);
  }
  
  .login-form .form-label {
    color: var(--gray-300);
  }
  
  .login-form .form-control {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: var(--gray-600);
    color: white;
  }
  
  .login-form .form-control:focus {
    background-color: rgba(255, 255, 255, 0.15);
  }
}
