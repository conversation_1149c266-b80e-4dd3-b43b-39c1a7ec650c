/* صفحة تسجيل الدخول - التصميم المخصص */
/* Login Page - Custom Styles */

.login-page {
  background: linear-gradient(135deg, var(--dark-blue) 0%, var(--dark-red) 100%);
  min-height: 100vh;
  font-family: var(--font-arabic);
  position: relative;
  overflow: hidden;
}

.login-page::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cpath fill='none' stroke='rgba(255,255,255,0.05)' stroke-width='1' d='M50,0 L50,100 M0,50 L100,50 M25,25 L75,75 M75,25 L25,75'/%3E%3Ccircle cx='50' cy='50' r='40' fill='none' stroke='rgba(255,255,255,0.1)' stroke-width='0.5'/%3E%3C/svg%3E");
  background-size: 50px 50px;
  opacity: 0.3;
  z-index: 0;
}

[dir="ltr"] .login-page {
  font-family: var(--font-english);
}

.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;
}

.login-form-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.25);
  max-width: 500px;
  margin: 0 auto;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.login-form-container::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
  z-index: -1;
}

.login-logo {
  width: 90px;
  height: 90px;
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-red) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  box-shadow: 0 10px 30px rgba(220, 53, 69, 0.3), 0 0 0 10px rgba(13, 110, 253, 0.1);
  position: relative;
  animation: pulse-shadow 3s infinite;
}

@keyframes pulse-shadow {
  0% {
    box-shadow: 0 10px 30px rgba(220, 53, 69, 0.3), 0 0 0 10px rgba(13, 110, 253, 0.1);
  }
  50% {
    box-shadow: 0 15px 40px rgba(220, 53, 69, 0.4), 0 0 0 15px rgba(13, 110, 253, 0.15);
  }
  100% {
    box-shadow: 0 10px 30px rgba(220, 53, 69, 0.3), 0 0 0 10px rgba(13, 110, 253, 0.1);
  }
}

.login-logo i {
  font-size: 2.5rem;
  color: white;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.login-title {
  color: var(--dark);
  font-weight: 800;
  margin-bottom: 0.5rem;
  font-size: 2rem;
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-red) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  letter-spacing: -0.5px;
}

.login-subtitle {
  color: var(--gray-600);
  margin-bottom: 2rem;
  font-size: 1.1rem;
  position: relative;
  display: inline-block;
}

.login-subtitle::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-red) 100%);
  border-radius: 3px;
}

.login-form .form-label {
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  font-size: 0.95rem;
  letter-spacing: 0.5px;
}

.login-form .form-label i {
  margin-right: 0.5rem;
  color: var(--primary-blue);
}

[dir="rtl"] .login-form .form-label i {
  margin-right: 0;
  margin-left: 0.5rem;
}

.login-form .form-control {
  border: 2px solid var(--gray-300);
  border-radius: 12px;
  padding: 0.85rem 1rem;
  font-size: 1rem;
  transition: all 0.3s ease;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
}

.login-form .form-control:focus {
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.15), inset 0 2px 4px rgba(0, 0, 0, 0.05);
  background-color: white;
  transform: translateY(-1px);
}

.login-form .input-group .btn {
  border-top-right-radius: 12px;
  border-bottom-right-radius: 12px;
  border: 2px solid var(--gray-300);
  border-left: none;
  background-color: white;
}

[dir="rtl"] .login-form .input-group .btn {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: 12px;
  border-bottom-left-radius: 12px;
  border-left: 2px solid var(--gray-300);
  border-right: none;
}

.login-form .btn-primary {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-red) 100%);
  border: none;
  border-radius: 12px;
  padding: 0.85rem 1.5rem;
  font-weight: 700;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.login-form .btn-primary::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
  z-index: -1;
  transition: all 0.6s ease;
}

.login-form .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, var(--secondary-blue) 0%, var(--secondary-red) 100%);
}

.login-form .btn-primary:hover::before {
  left: 100%;
}

.form-check-input {
  width: 1.2rem;
  height: 1.2rem;
  margin-top: 0.15rem;
  border: 2px solid var(--gray-400);
  transition: all 0.3s ease;
}

.form-check-input:checked {
  background-color: var(--primary-blue);
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 2px rgba(13, 110, 253, 0.2);
}

.form-check-label {
  font-size: 0.95rem;
  color: var(--gray-600);
}

.language-switcher {
  margin-top: 2rem;
}

.language-switcher .btn {
  margin: 0 0.25rem;
  border-radius: 20px;
  padding: 0.5rem 1.2rem;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  font-weight: 500;
  border: 2px solid var(--gray-300);
}

.language-switcher .btn:hover {
  border-color: var(--primary-blue);
  color: var(--primary-blue);
}

.language-switcher .btn.active {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-red) 100%);
  color: white;
  border-color: transparent;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

/* الجانب الأيمن - الخلفية */
.login-bg {
  background: linear-gradient(135deg,
    rgba(13, 110, 253, 0.95) 0%,
    rgba(220, 53, 69, 0.95) 100%);
  background-size: cover;
  background-position: center;
  height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.login-bg::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='60' viewBox='0 0 60 60'%3E%3Cpath fill='none' stroke='rgba(255,255,255,0.1)' stroke-width='1' d='M30,0 L30,60 M0,30 L60,30 M15,15 L45,45 M45,15 L15,45'/%3E%3Ccircle cx='30' cy='30' r='20' fill='none' stroke='rgba(255,255,255,0.15)' stroke-width='0.5'/%3E%3C/svg%3E");
  background-size: 60px 60px;
  opacity: 0.5;
  z-index: 0;
}

.login-bg::after {
  content: "";
  position: absolute;
  width: 800px;
  height: 800px;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
  border-radius: 50%;
  top: -400px;
  right: -400px;
  z-index: 0;
  animation: pulse-light 15s infinite alternate ease-in-out;
}

@keyframes pulse-light {
  0% {
    opacity: 0.3;
    transform: scale(1);
  }
  100% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

.login-bg-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.login-bg-content {
  text-align: center;
  color: white;
  padding: 2rem;
  max-width: 500px;
  position: relative;
  z-index: 2;
}

.login-bg-content h3 {
  font-size: 2.8rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  line-height: 1.2;
  position: relative;
  display: inline-block;
}

.login-bg-content h3::after {
  content: "";
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.login-bg-content p {
  font-size: 1.3rem;
  margin-bottom: 2.5rem;
  opacity: 0.9;
  line-height: 1.7;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.features-list {
  text-align: right;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

[dir="ltr"] .features-list {
  text-align: left;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 1.2rem;
  font-size: 1.15rem;
  opacity: 0.95;
  transition: all 0.3s ease;
}

.feature-item:last-child {
  margin-bottom: 0;
}

.feature-item:hover {
  transform: translateX(-5px);
}

[dir="ltr"] .feature-item:hover {
  transform: translateX(5px);
}

.feature-item i {
  margin-left: 1rem;
  color: white;
  font-size: 1.3rem;
  background: rgba(40, 167, 69, 0.8);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

[dir="ltr"] .feature-item i {
  margin-left: 0;
  margin-right: 1rem;
}

/* تأثيرات الحركة */
@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.login-form-container {
  animation: slideInFromLeft 0.8s ease-out;
}

.login-bg-content {
  animation: slideInFromRight 0.8s ease-out;
}

/* التصميم المتجاوب */
@media (max-width: 992px) {
  .login-bg {
    display: none;
  }

  .login-form-container {
    margin: 2rem auto;
    padding: 2rem;
  }

  .login-title {
    font-size: 1.5rem;
  }

  .login-bg-content h3 {
    font-size: 2rem;
  }
}

@media (max-width: 576px) {
  .login-form-container {
    margin: 1rem;
    padding: 1.5rem;
    border-radius: 15px;
  }

  .login-logo {
    width: 60px;
    height: 60px;
  }

  .login-logo i {
    font-size: 1.5rem;
  }

  .login-title {
    font-size: 1.3rem;
  }

  .login-subtitle {
    font-size: 0.9rem;
  }
}

/* تأثيرات إضافية */
.login-form .form-control:focus {
  animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

.btn-primary:active {
  transform: translateY(0) !important;
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
  .login-form-container {
    background: rgba(33, 37, 41, 0.95);
    color: white;
  }

  .login-title {
    color: white;
  }

  .login-subtitle {
    color: var(--gray-400);
  }

  .login-form .form-label {
    color: var(--gray-300);
  }

  .login-form .form-control {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: var(--gray-600);
    color: white;
  }

  .login-form .form-control:focus {
    background-color: rgba(255, 255, 255, 0.15);
  }
}
