#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نماذج قاعدة البيانات
Database Models

تحتوي على جميع نماذج قاعدة البيانات للنظام
"""

from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date
from decimal import Decimal

# استيراد db من app.py
from app import db

class User(UserMixin, db.Model):
    """نموذج المستخدمين"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name_ar = db.Column(db.String(200), nullable=False)
    full_name_en = db.Column(db.String(200))
    phone = db.Column(db.String(20))
    role = db.Column(db.String(20), default='cashier')  # admin, manager, cashier
    is_active = db.Column(db.Boolean, default=True)
    last_login = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def set_password(self, password):
        """تعيين كلمة المرور"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """التحقق من كلمة المرور"""
        return check_password_hash(self.password_hash, password)
    
    def has_permission(self, permission):
        """التحقق من الصلاحيات"""
        permissions = {
            'admin': ['all'],
            'manager': ['sales', 'inventory', 'customers', 'suppliers', 'reports'],
            'cashier': ['sales', 'customers']
        }
        return permission in permissions.get(self.role, []) or 'all' in permissions.get(self.role, [])
    
    def __repr__(self):
        return f'<User {self.username}>'

class Category(db.Model):
    """نموذج فئات المنتجات"""
    __tablename__ = 'categories'
    
    id = db.Column(db.Integer, primary_key=True)
    name_ar = db.Column(db.String(100), nullable=False)
    name_en = db.Column(db.String(100))
    description_ar = db.Column(db.Text)
    description_en = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    products = db.relationship('Product', backref='category', lazy=True)
    
    def __repr__(self):
        return f'<Category {self.name_ar}>'

class Product(db.Model):
    """نموذج المنتجات"""
    __tablename__ = 'products'
    
    id = db.Column(db.Integer, primary_key=True)
    name_ar = db.Column(db.String(200), nullable=False)
    name_en = db.Column(db.String(200))
    category_id = db.Column(db.Integer, db.ForeignKey('categories.id'))
    barcode = db.Column(db.String(50), unique=True)
    
    # الأسعار والتكاليف
    price = db.Column(db.Numeric(10, 2), nullable=False, default=0)
    cost = db.Column(db.Numeric(10, 2), nullable=False, default=0)
    
    # المخزون
    stock_quantity = db.Column(db.Integer, default=0)
    min_stock_level = db.Column(db.Integer, default=10)
    max_stock_level = db.Column(db.Integer, default=1000)
    
    # معلومات إضافية
    expiry_date = db.Column(db.Date)
    batch_number = db.Column(db.String(50))
    manufacturer_ar = db.Column(db.String(200))
    manufacturer_en = db.Column(db.String(200))
    description_ar = db.Column(db.Text)
    description_en = db.Column(db.Text)
    
    # وحدات البيع المتعددة
    unit_ar = db.Column(db.String(50), default='قطعة')
    unit_en = db.Column(db.String(50), default='piece')
    
    # أنواع البيع (للأدوية: حبة، شريط، علبة - للحقن: حقنة، علبة)
    sale_units = db.Column(db.JSON)  # مثال: {"piece": {"name_ar": "حبة", "name_en": "piece", "factor": 1}, "strip": {"name_ar": "شريط", "name_en": "strip", "factor": 10}}
    
    # الحالة
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    sale_items = db.relationship('SaleItem', backref='product', lazy=True)
    purchase_items = db.relationship('PurchaseItem', backref='product', lazy=True)
    
    @property
    def is_low_stock(self):
        """التحقق من انخفاض المخزون"""
        return self.stock_quantity <= self.min_stock_level
    
    @property
    def is_expired(self):
        """التحقق من انتهاء الصلاحية"""
        if self.expiry_date:
            return self.expiry_date <= date.today()
        return False
    
    def get_sale_units(self):
        """الحصول على وحدات البيع"""
        if self.sale_units:
            return self.sale_units
        return {
            "piece": {
                "name_ar": "قطعة",
                "name_en": "piece",
                "factor": 1,
                "price": float(self.price)
            }
        }
    
    def __repr__(self):
        return f'<Product {self.name_ar}>'

class Customer(db.Model):
    """نموذج العملاء"""
    __tablename__ = 'customers'
    
    id = db.Column(db.Integer, primary_key=True)
    name_ar = db.Column(db.String(200), nullable=False)
    name_en = db.Column(db.String(200))
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    address_ar = db.Column(db.Text)
    address_en = db.Column(db.Text)
    national_id = db.Column(db.String(20))
    birth_date = db.Column(db.Date)
    gender = db.Column(db.String(10))  # male, female
    notes_ar = db.Column(db.Text)
    notes_en = db.Column(db.Text)
    total_purchases = db.Column(db.Numeric(12, 2), default=0)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    sales = db.relationship('Sale', backref='customer', lazy=True)
    
    def __repr__(self):
        return f'<Customer {self.name_ar}>'

class Supplier(db.Model):
    """نموذج الموردين"""
    __tablename__ = 'suppliers'
    
    id = db.Column(db.Integer, primary_key=True)
    name_ar = db.Column(db.String(200), nullable=False)
    name_en = db.Column(db.String(200))
    company_ar = db.Column(db.String(200))
    company_en = db.Column(db.String(200))
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    address_ar = db.Column(db.Text)
    address_en = db.Column(db.Text)
    tax_number = db.Column(db.String(50))
    contact_person_ar = db.Column(db.String(200))
    contact_person_en = db.Column(db.String(200))
    notes_ar = db.Column(db.Text)
    notes_en = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    purchases = db.relationship('Purchase', backref='supplier', lazy=True)
    
    def __repr__(self):
        return f'<Supplier {self.name_ar}>'

class Sale(db.Model):
    """نموذج المبيعات"""
    __tablename__ = 'sales'
    
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # المبالغ
    subtotal = db.Column(db.Numeric(12, 2), default=0)
    discount_amount = db.Column(db.Numeric(12, 2), default=0)
    discount_percentage = db.Column(db.Numeric(5, 2), default=0)
    tax_amount = db.Column(db.Numeric(12, 2), default=0)
    tax_percentage = db.Column(db.Numeric(5, 2), default=14)  # ضريبة القيمة المضافة في مصر
    total_amount = db.Column(db.Numeric(12, 2), default=0)
    
    # الدفع
    payment_method = db.Column(db.String(20), default='cash')  # cash, card, credit
    payment_status = db.Column(db.String(20), default='paid')  # paid, pending, partial
    paid_amount = db.Column(db.Numeric(12, 2), default=0)
    change_amount = db.Column(db.Numeric(12, 2), default=0)
    
    # معلومات إضافية
    notes_ar = db.Column(db.Text)
    notes_en = db.Column(db.Text)
    is_suspended = db.Column(db.Boolean, default=False)  # للفواتير المعلقة
    is_returned = db.Column(db.Boolean, default=False)
    
    # التواريخ
    sale_date = db.Column(db.DateTime, default=datetime.utcnow)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    items = db.relationship('SaleItem', backref='sale', lazy=True, cascade='all, delete-orphan')
    user = db.relationship('User', backref='sales')
    
    def calculate_totals(self):
        """حساب المجاميع"""
        self.subtotal = sum(item.total_price for item in self.items)
        
        # حساب الخصم
        if self.discount_percentage > 0:
            self.discount_amount = self.subtotal * (self.discount_percentage / 100)
        
        # حساب الضريبة
        taxable_amount = self.subtotal - self.discount_amount
        self.tax_amount = taxable_amount * (self.tax_percentage / 100)
        
        # المجموع النهائي
        self.total_amount = self.subtotal - self.discount_amount + self.tax_amount
    
    def __repr__(self):
        return f'<Sale {self.invoice_number}>'

class SaleItem(db.Model):
    """نموذج عناصر المبيعات"""
    __tablename__ = 'sale_items'
    
    id = db.Column(db.Integer, primary_key=True)
    sale_id = db.Column(db.Integer, db.ForeignKey('sales.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)
    
    quantity = db.Column(db.Integer, nullable=False, default=1)
    unit_type = db.Column(db.String(20), default='piece')  # piece, strip, box
    unit_factor = db.Column(db.Integer, default=1)  # عامل التحويل
    unit_price = db.Column(db.Numeric(10, 2), nullable=False)
    total_price = db.Column(db.Numeric(12, 2), nullable=False)
    discount_amount = db.Column(db.Numeric(10, 2), default=0)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def calculate_total(self):
        """حساب المجموع"""
        self.total_price = (self.quantity * self.unit_price) - self.discount_amount
    
    def __repr__(self):
        return f'<SaleItem {self.product.name_ar} x {self.quantity}>'

class Purchase(db.Model):
    """نموذج المشتريات"""
    __tablename__ = 'purchases'
    
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # المبالغ
    subtotal = db.Column(db.Numeric(12, 2), default=0)
    discount_amount = db.Column(db.Numeric(12, 2), default=0)
    tax_amount = db.Column(db.Numeric(12, 2), default=0)
    total_amount = db.Column(db.Numeric(12, 2), default=0)
    
    # الدفع
    payment_status = db.Column(db.String(20), default='pending')  # paid, pending, partial
    paid_amount = db.Column(db.Numeric(12, 2), default=0)
    
    # معلومات إضافية
    notes_ar = db.Column(db.Text)
    notes_en = db.Column(db.Text)
    
    # التواريخ
    purchase_date = db.Column(db.DateTime, default=datetime.utcnow)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    items = db.relationship('PurchaseItem', backref='purchase', lazy=True, cascade='all, delete-orphan')
    user = db.relationship('User', backref='purchases')
    
    def __repr__(self):
        return f'<Purchase {self.invoice_number}>'

class PurchaseItem(db.Model):
    """نموذج عناصر المشتريات"""
    __tablename__ = 'purchase_items'
    
    id = db.Column(db.Integer, primary_key=True)
    purchase_id = db.Column(db.Integer, db.ForeignKey('purchases.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)
    
    quantity = db.Column(db.Integer, nullable=False, default=1)
    unit_cost = db.Column(db.Numeric(10, 2), nullable=False)
    total_cost = db.Column(db.Numeric(12, 2), nullable=False)
    expiry_date = db.Column(db.Date)
    batch_number = db.Column(db.String(50))
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<PurchaseItem {self.product.name_ar} x {self.quantity}>'
