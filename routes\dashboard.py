#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مسارات لوحة التحكم
Dashboard Routes

تحتوي على مسارات لوحة التحكم الرئيسية والإحصائيات
"""

from flask import Blueprint, render_template, jsonify
from flask_login import login_required, current_user
from sqlalchemy import func, and_
from datetime import datetime, date, timedelta
from models import Product, Sale, SaleItem, Customer, Supplier, Purchase
from app import db

bp = Blueprint('dashboard', __name__, url_prefix='/dashboard')

@bp.route('/')
@login_required
def index():
    """لوحة التحكم الرئيسية"""
    
    # الإحصائيات الأساسية
    stats = get_dashboard_stats()
    
    # المنتجات منخفضة المخزون
    low_stock_products = Product.query.filter(
        and_(Product.stock_quantity <= Product.min_stock_level, Product.is_active == True)
    ).limit(10).all()
    
    # المنتجات منتهية الصلاحية
    expired_products = Product.query.filter(
        and_(Product.expiry_date <= date.today(), Product.is_active == True)
    ).limit(10).all()
    
    # آخر المبيعات
    recent_sales = Sale.query.filter_by(is_suspended=False).order_by(Sale.created_at.desc()).limit(10).all()
    
    # الفواتير المعلقة
    suspended_sales = Sale.query.filter_by(is_suspended=True).count()
    
    return render_template('dashboard/index.html',
                         stats=stats,
                         low_stock_products=low_stock_products,
                         expired_products=expired_products,
                         recent_sales=recent_sales,
                         suspended_sales=suspended_sales)

@bp.route('/api/stats')
@login_required
def api_stats():
    """API للحصول على الإحصائيات"""
    return jsonify(get_dashboard_stats())

@bp.route('/api/sales_chart')
@login_required
def api_sales_chart():
    """API لبيانات مخطط المبيعات"""
    
    # المبيعات لآخر 7 أيام
    end_date = date.today()
    start_date = end_date - timedelta(days=6)
    
    sales_data = []
    current_date = start_date
    
    while current_date <= end_date:
        daily_sales = db.session.query(func.sum(Sale.total_amount)).filter(
            and_(
                func.date(Sale.sale_date) == current_date,
                Sale.is_suspended == False
            )
        ).scalar() or 0
        
        sales_data.append({
            'date': current_date.strftime('%Y-%m-%d'),
            'date_ar': current_date.strftime('%d/%m'),
            'amount': float(daily_sales)
        })
        
        current_date += timedelta(days=1)
    
    return jsonify(sales_data)

@bp.route('/api/top_products')
@login_required
def api_top_products():
    """API للمنتجات الأكثر مبيعاً"""
    
    # المنتجات الأكثر مبيعاً لهذا الشهر
    start_of_month = date.today().replace(day=1)
    
    top_products = db.session.query(
        Product.name_ar,
        func.sum(SaleItem.quantity).label('total_quantity'),
        func.sum(SaleItem.total_price).label('total_amount')
    ).join(SaleItem).join(Sale).filter(
        and_(
            Sale.sale_date >= start_of_month,
            Sale.is_suspended == False
        )
    ).group_by(Product.id).order_by(func.sum(SaleItem.total_price).desc()).limit(10).all()
    
    return jsonify([{
        'name': product.name_ar,
        'quantity': int(product.total_quantity),
        'amount': float(product.total_amount)
    } for product in top_products])

def get_dashboard_stats():
    """الحصول على إحصائيات لوحة التحكم"""
    
    today = date.today()
    start_of_month = today.replace(day=1)
    start_of_year = today.replace(month=1, day=1)
    
    # إجمالي المنتجات
    total_products = Product.query.filter_by(is_active=True).count()
    
    # المنتجات منخفضة المخزون
    low_stock_count = Product.query.filter(
        and_(Product.stock_quantity <= Product.min_stock_level, Product.is_active == True)
    ).count()
    
    # المنتجات منتهية الصلاحية
    expired_count = Product.query.filter(
        and_(Product.expiry_date <= today, Product.is_active == True)
    ).count()
    
    # مبيعات اليوم
    today_sales = db.session.query(func.sum(Sale.total_amount)).filter(
        and_(func.date(Sale.sale_date) == today, Sale.is_suspended == False)
    ).scalar() or 0
    
    # مبيعات الشهر
    month_sales = db.session.query(func.sum(Sale.total_amount)).filter(
        and_(Sale.sale_date >= start_of_month, Sale.is_suspended == False)
    ).scalar() or 0
    
    # مبيعات السنة
    year_sales = db.session.query(func.sum(Sale.total_amount)).filter(
        and_(Sale.sale_date >= start_of_year, Sale.is_suspended == False)
    ).scalar() or 0
    
    # عدد العملاء
    total_customers = Customer.query.filter_by(is_active=True).count()
    
    # عدد الموردين
    total_suppliers = Supplier.query.filter_by(is_active=True).count()
    
    # عدد الفواتير المعلقة
    suspended_invoices = Sale.query.filter_by(is_suspended=True).count()
    
    # قيمة المخزون
    inventory_value = db.session.query(func.sum(Product.stock_quantity * Product.cost)).filter(
        Product.is_active == True
    ).scalar() or 0
    
    return {
        'total_products': total_products,
        'low_stock_count': low_stock_count,
        'expired_count': expired_count,
        'today_sales': float(today_sales),
        'month_sales': float(month_sales),
        'year_sales': float(year_sales),
        'total_customers': total_customers,
        'total_suppliers': total_suppliers,
        'suspended_invoices': suspended_invoices,
        'inventory_value': float(inventory_value)
    }
