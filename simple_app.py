from flask import Flask, render_template_string

app = Flask(__name__)

@app.route('/')
def index():
    return render_template_string('''
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>نظام إدارة الصيدليات</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #0d6efd 0%, #dc3545 100%);
                margin: 0;
                padding: 0;
                height: 100vh;
                display: flex;
                justify-content: center;
                align-items: center;
                color: white;
                text-align: center;
            }
            .container {
                background-color: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                padding: 2rem;
                border-radius: 10px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
                max-width: 800px;
            }
            h1 {
                font-size: 2.5rem;
                margin-bottom: 1rem;
            }
            p {
                font-size: 1.2rem;
                margin-bottom: 2rem;
                opacity: 0.9;
            }
            .features {
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
                gap: 1rem;
                margin-top: 2rem;
            }
            .feature {
                background-color: rgba(255, 255, 255, 0.2);
                padding: 1rem;
                border-radius: 5px;
                width: 200px;
                text-align: center;
            }
            .feature i {
                font-size: 2rem;
                margin-bottom: 0.5rem;
            }
            .language-switcher {
                position: absolute;
                top: 1rem;
                left: 1rem;
            }
            .language-switcher a {
                color: white;
                text-decoration: none;
                margin-right: 1rem;
            }
            .language-switcher a:hover {
                text-decoration: underline;
            }
        </style>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    </head>
    <body>
        <div class="language-switcher">
            <a href="?lang=ar">العربية</a>
            <a href="?lang=en">English</a>
        </div>
        
        <div class="container">
            <h1>نظام إدارة الصيدليات الاحترافي</h1>
            <p>نظام شامل ومتطور لإدارة جميع عمليات الصيدلية بكفاءة وسهولة</p>
            
            <div class="features">
                <div class="feature">
                    <i class="fas fa-cash-register"></i>
                    <h3>نقطة بيع احترافية</h3>
                </div>
                <div class="feature">
                    <i class="fas fa-boxes"></i>
                    <h3>إدارة المخزون الذكية</h3>
                </div>
                <div class="feature">
                    <i class="fas fa-chart-bar"></i>
                    <h3>تقارير شاملة</h3>
                </div>
                <div class="feature">
                    <i class="fas fa-globe"></i>
                    <h3>دعم متعدد اللغات</h3>
                </div>
            </div>
        </div>
    </body>
    </html>
    ''')

if __name__ == '__main__':
    app.run(debug=True)
