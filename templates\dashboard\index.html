{% extends "base.html" %}

{% block title %}
    {% if CURRENT_LANGUAGE == 'ar' %}لوحة التحكم - {{ APP_NAME }}{% else %}Dashboard - {{ APP_NAME_EN }}{% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            {% if CURRENT_LANGUAGE == 'ar' %}لوحة التحكم{% else %}Dashboard{% endif %}
        </h1>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-primary" onclick="location.reload()">
                <i class="fas fa-sync-alt"></i>
                {% if CURRENT_LANGUAGE == 'ar' %}تحديث{% else %}Refresh{% endif %}
            </button>
            <a href="{{ url_for('pos.index') }}" class="btn btn-primary">
                <i class="fas fa-cash-register"></i>
                {% if CURRENT_LANGUAGE == 'ar' %}نقطة البيع{% else %}POS{% endif %}
            </a>
        </div>
    </div>

    <!-- بطاقات الإحصائيات الرئيسية -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-value" id="today-sales">{{ "%.2f"|format(stats.today_sales) }}</div>
                            <div class="stat-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}مبيعات اليوم (ج.م){% else %}Today's Sales (EGP){% endif %}
                            </div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card success">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-value" id="total-products">{{ stats.total_products }}</div>
                            <div class="stat-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}إجمالي المنتجات{% else %}Total Products{% endif %}
                            </div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-boxes"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card warning">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-value" id="low-stock">{{ stats.low_stock_count }}</div>
                            <div class="stat-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}منتجات منخفضة المخزون{% else %}Low Stock Products{% endif %}
                            </div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card danger">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-value" id="expired-products">{{ stats.expired_count }}</div>
                            <div class="stat-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}منتجات منتهية الصلاحية{% else %}Expired Products{% endif %}
                            </div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-calendar-times"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الصف الثاني من الإحصائيات -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-value">{{ stats.total_customers }}</div>
                            <div class="stat-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}العملاء{% else %}Customers{% endif %}
                            </div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-value">{{ stats.total_suppliers }}</div>
                            <div class="stat-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}الموردين{% else %}Suppliers{% endif %}
                            </div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-industry"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card warning">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-value">{{ stats.suspended_invoices }}</div>
                            <div class="stat-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}فواتير معلقة{% else %}Suspended Invoices{% endif %}
                            </div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-pause-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card success">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-value">{{ "%.0f"|format(stats.inventory_value) }}</div>
                            <div class="stat-label">
                                {% if CURRENT_LANGUAGE == 'ar' %}قيمة المخزون (ج.م){% else %}Inventory Value (EGP){% endif %}
                            </div>
                        </div>
                        <div class="stat-icon">
                            <i class="fas fa-warehouse"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسوم البيانية والجداول -->
    <div class="row">
        <!-- مخطط المبيعات -->
        <div class="col-xl-8 col-lg-7 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold">
                        {% if CURRENT_LANGUAGE == 'ar' %}مبيعات آخر 7 أيام{% else %}Sales Last 7 Days{% endif %}
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="salesChart" width="100%" height="40"></canvas>
                </div>
            </div>
        </div>

        <!-- المنتجات الأكثر مبيعاً -->
        <div class="col-xl-4 col-lg-5 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold">
                        {% if CURRENT_LANGUAGE == 'ar' %}المنتجات الأكثر مبيعاً{% else %}Top Selling Products{% endif %}
                    </h6>
                </div>
                <div class="card-body">
                    <div id="topProductsList">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- التنبيهات والإشعارات -->
    <div class="row">
        <!-- المنتجات منخفضة المخزون -->
        {% if low_stock_products %}
        <div class="col-xl-6 col-lg-6 mb-4">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        {% if CURRENT_LANGUAGE == 'ar' %}منتجات منخفضة المخزون{% else %}Low Stock Products{% endif %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>{% if CURRENT_LANGUAGE == 'ar' %}المنتج{% else %}Product{% endif %}</th>
                                    <th>{% if CURRENT_LANGUAGE == 'ar' %}المخزون{% else %}Stock{% endif %}</th>
                                    <th>{% if CURRENT_LANGUAGE == 'ar' %}الحد الأدنى{% else %}Min Level{% endif %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in low_stock_products %}
                                <tr>
                                    <td>{{ product.name_ar }}</td>
                                    <td><span class="badge bg-warning">{{ product.stock_quantity }}</span></td>
                                    <td>{{ product.min_stock_level }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- المنتجات منتهية الصلاحية -->
        {% if expired_products %}
        <div class="col-xl-6 col-lg-6 mb-4">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-calendar-times me-2"></i>
                        {% if CURRENT_LANGUAGE == 'ar' %}منتجات منتهية الصلاحية{% else %}Expired Products{% endif %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>{% if CURRENT_LANGUAGE == 'ar' %}المنتج{% else %}Product{% endif %}</th>
                                    <th>{% if CURRENT_LANGUAGE == 'ar' %}تاريخ الانتهاء{% else %}Expiry Date{% endif %}</th>
                                    <th>{% if CURRENT_LANGUAGE == 'ar' %}المخزون{% else %}Stock{% endif %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in expired_products %}
                                <tr>
                                    <td>{{ product.name_ar }}</td>
                                    <td>{{ product.expiry_date.strftime('%Y-%m-%d') if product.expiry_date }}</td>
                                    <td><span class="badge bg-danger">{{ product.stock_quantity }}</span></td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- آخر المبيعات -->
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold">
                        {% if CURRENT_LANGUAGE == 'ar' %}آخر المبيعات{% else %}Recent Sales{% endif %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>{% if CURRENT_LANGUAGE == 'ar' %}رقم الفاتورة{% else %}Invoice No.{% endif %}</th>
                                    <th>{% if CURRENT_LANGUAGE == 'ar' %}العميل{% else %}Customer{% endif %}</th>
                                    <th>{% if CURRENT_LANGUAGE == 'ar' %}المبلغ{% else %}Amount{% endif %}</th>
                                    <th>{% if CURRENT_LANGUAGE == 'ar' %}التاريخ{% else %}Date{% endif %}</th>
                                    <th>{% if CURRENT_LANGUAGE == 'ar' %}الكاشير{% else %}Cashier{% endif %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for sale in recent_sales %}
                                <tr>
                                    <td>{{ sale.invoice_number }}</td>
                                    <td>{{ sale.customer.name_ar if sale.customer else 'عميل نقدي' }}</td>
                                    <td>{{ "%.2f"|format(sale.total_amount) }} ج.م</td>
                                    <td>{{ sale.sale_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>{{ sale.user.full_name_ar }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحميل بيانات الرسم البياني
document.addEventListener('DOMContentLoaded', function() {
    loadSalesChart();
    loadTopProducts();
    
    // تحديث الإحصائيات كل 30 ثانية
    setInterval(updateStats, 30000);
});

function loadSalesChart() {
    fetch('/dashboard/api/sales_chart')
        .then(response => response.json())
        .then(data => {
            const ctx = document.getElementById('salesChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.map(item => item.date_ar),
                    datasets: [{
                        label: '{% if CURRENT_LANGUAGE == "ar" %}المبيعات (ج.م){% else %}Sales (EGP){% endif %}',
                        data: data.map(item => item.amount),
                        borderColor: 'rgb(13, 110, 253)',
                        backgroundColor: 'rgba(13, 110, 253, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString('ar-EG') + ' ج.م';
                                }
                            }
                        }
                    }
                }
            });
        })
        .catch(error => console.error('Error loading sales chart:', error));
}

function loadTopProducts() {
    fetch('/dashboard/api/top_products')
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('topProductsList');
            if (data.length === 0) {
                container.innerHTML = '<p class="text-muted text-center">لا توجد بيانات</p>';
                return;
            }
            
            let html = '<div class="list-group list-group-flush">';
            data.forEach((product, index) => {
                html += `
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">${product.name}</h6>
                            <small class="text-muted">الكمية: ${product.quantity}</small>
                        </div>
                        <span class="badge bg-primary rounded-pill">${product.amount.toFixed(2)} ج.م</span>
                    </div>
                `;
            });
            html += '</div>';
            container.innerHTML = html;
        })
        .catch(error => {
            console.error('Error loading top products:', error);
            document.getElementById('topProductsList').innerHTML = '<p class="text-danger text-center">خطأ في تحميل البيانات</p>';
        });
}

function updateStats() {
    fetch('/dashboard/api/stats')
        .then(response => response.json())
        .then(data => {
            document.getElementById('today-sales').textContent = data.today_sales.toFixed(2);
            document.getElementById('total-products').textContent = data.total_products;
            document.getElementById('low-stock').textContent = data.low_stock_count;
            document.getElementById('expired-products').textContent = data.expired_count;
        })
        .catch(error => console.error('Error updating stats:', error));
}
</script>
{% endblock %}
